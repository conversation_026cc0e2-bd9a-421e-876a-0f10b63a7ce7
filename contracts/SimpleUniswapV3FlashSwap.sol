// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "hardhat/console.sol";

// Minimal Uniswap V3 interfaces
interface IUniswapV3Pool {
    function flash(
        address recipient,
        uint256 amount0,
        uint256 amount1,
        bytes calldata data
    ) external;
    
    function token0() external view returns (address);
    function token1() external view returns (address);
    function fee() external view returns (uint24);
}

interface IUniswapV3Factory {
    function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params) external payable returns (uint256 amountOut);
}

/**
 * @title SimpleUniswapV3FlashSwap
 * @dev Simplified contract for Uniswap V3 flash swaps
 */
contract SimpleUniswapV3FlashSwap is Ownable {
    
    // Uniswap V3 addresses (mainnet)
    IUniswapV3Factory public constant FACTORY = IUniswapV3Factory(0x1F98431c8aD98523631AE4a59f267346ea31F984);
    IUniswapV3Router public constant ROUTER = IUniswapV3Router(0xE592427A0AEce92De3Edee1F18E0157C05861564);
    
    struct FlashSwapParams {
        address tokenA;
        address tokenB;
        uint24 borrowFee;
        uint24 sellFee;
        uint256 amount;
        uint256 minProfit;
        bool isToken0;
    }
    
    event FlashSwapExecuted(
        address indexed tokenA,
        address indexed tokenB,
        uint24 borrowFee,
        uint24 sellFee,
        uint256 amount,
        uint256 profit
    );
    
    constructor() Ownable(msg.sender) {
        console.log("SimpleUniswapV3FlashSwap deployed by:", msg.sender);
    }
    
    /**
     * @dev Execute flash swap arbitrage between different fee tiers
     */
    function executeFlashSwapArbitrage(
        address tokenA,
        address tokenB,
        uint24 borrowFee,
        uint24 sellFee,
        uint256 amount,
        uint256 minProfit
    ) external onlyOwner {
        require(tokenA != tokenB, "Tokens must be different");
        require(amount > 0, "Amount must be greater than 0");
        require(borrowFee != sellFee, "Fee tiers must be different");
        
        // Get pool addresses
        address borrowPool = FACTORY.getPool(tokenA, tokenB, borrowFee);
        address sellPool = FACTORY.getPool(tokenA, tokenB, sellFee);
        
        require(borrowPool != address(0), "Borrow pool does not exist");
        require(sellPool != address(0), "Sell pool does not exist");
        
        console.log("Flash swap arbitrage started");
        console.log("TokenA:", tokenA);
        console.log("TokenB:", tokenB);
        console.log("Borrow Pool:", borrowPool);
        console.log("Sell Pool:", sellPool);
        
        // Determine token order in the pool
        address token0 = IUniswapV3Pool(borrowPool).token0();
        bool isToken0 = (tokenA == token0);
        
        // Prepare flash swap parameters
        FlashSwapParams memory params = FlashSwapParams({
            tokenA: tokenA,
            tokenB: tokenB,
            borrowFee: borrowFee,
            sellFee: sellFee,
            amount: amount,
            minProfit: minProfit,
            isToken0: isToken0
        });
        
        // Execute flash swap
        uint256 amount0 = isToken0 ? amount : 0;
        uint256 amount1 = isToken0 ? 0 : amount;
        
        IUniswapV3Pool(borrowPool).flash(
            address(this),
            amount0,
            amount1,
            abi.encode(params)
        );
    }
    
    /**
     * @dev Uniswap V3 flash callback
     */
    function uniswapV3FlashCallback(
        uint256 fee0,
        uint256 fee1,
        bytes calldata data
    ) external {
        console.log("Flash callback started");
        console.log("Fee0:", fee0);
        console.log("Fee1:", fee1);
        
        // Decode parameters
        FlashSwapParams memory params = abi.decode(data, (FlashSwapParams));
        
        // Verify the caller is a valid Uniswap V3 pool
        address expectedPool = FACTORY.getPool(params.tokenA, params.tokenB, params.borrowFee);
        require(msg.sender == expectedPool, "Invalid pool caller");
        
        // Simple arbitrage execution
        uint256 amountOut = _executeArbitrageSwap(params);
        
        // Calculate repayment amount
        uint256 repayAmount = params.amount + (params.isToken0 ? fee0 : fee1);
        
        // Check if we have enough to repay
        uint256 finalBalance = IERC20(params.tokenA).balanceOf(address(this));
        require(finalBalance >= repayAmount, "Insufficient balance to repay");
        
        uint256 profit = finalBalance - repayAmount;
        require(profit >= params.minProfit, "Insufficient profit");
        
        // Repay the flash swap
        IERC20(params.tokenA).transfer(msg.sender, repayAmount);
        
        emit FlashSwapExecuted(
            params.tokenA,
            params.tokenB,
            params.borrowFee,
            params.sellFee,
            params.amount,
            profit
        );
        
        console.log("Flash swap completed successfully");
    }
    
    /**
     * @dev Execute the arbitrage swap
     */
    function _executeArbitrageSwap(FlashSwapParams memory params) internal returns (uint256 amountOut) {
        // Approve router to spend the borrowed token
        IERC20(params.tokenA).approve(address(ROUTER), params.amount);
        
        // Execute swap: tokenA -> tokenB on sellFee tier
        IUniswapV3Router.ExactInputSingleParams memory swapParams = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: params.tokenA,
            tokenOut: params.tokenB,
            fee: params.sellFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: params.amount,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });
        
        uint256 tokenBReceived = ROUTER.exactInputSingle(swapParams);
        
        // Approve router to spend tokenB
        IERC20(params.tokenB).approve(address(ROUTER), tokenBReceived);
        
        // Execute reverse swap: tokenB -> tokenA on borrowFee tier
        IUniswapV3Router.ExactInputSingleParams memory reverseSwapParams = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: params.tokenB,
            tokenOut: params.tokenA,
            fee: params.borrowFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: tokenBReceived,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });
        
        amountOut = ROUTER.exactInputSingle(reverseSwapParams);
        return amountOut;
    }
    
    /**
     * @dev Emergency function to withdraw tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
    
    /**
     * @dev Get pool address for a token pair and fee tier
     */
    function getPoolAddress(address tokenA, address tokenB, uint24 fee) external view returns (address pool) {
        return FACTORY.getPool(tokenA, tokenB, fee);
    }
}
