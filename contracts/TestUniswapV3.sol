// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "hardhat/console.sol";

// Minimal interface for testing
interface IUniswapV3Factory {
    function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool);
}

/**
 * @title TestUniswapV3
 * @dev Simple test contract to verify Uniswap V3 integration
 */
contract TestUniswapV3 is Ownable {
    
    // Uniswap V3 Factory address (mainnet)
    IUniswapV3Factory public constant FACTORY = IUniswapV3Factory(******************************************);
    
    // Token addresses (mainnet)
    address public constant WETH = ******************************************;
    address public constant USDC = ******************************************;
    
    event ContractDeployed(address indexed deployer, uint256 timestamp);
    event PoolAddressRetrieved(address indexed tokenA, address indexed tokenB, uint24 fee, address pool);
    
    constructor() Ownable(msg.sender) {
        console.log("TestUniswapV3 deployed by:", msg.sender);
        console.log("Factory address:", address(FACTORY));
        console.log("WETH address:", WETH);
        console.log("USDC address:", USDC);
        
        emit ContractDeployed(msg.sender, block.timestamp);
    }
    
    /**
     * @dev Get pool address for a token pair and fee tier
     */
    function getPoolAddress(address tokenA, address tokenB, uint24 fee) external view returns (address pool) {
        return FACTORY.getPool(tokenA, tokenB, fee);
    }
    
    /**
     * @dev Test function to get WETH/USDC pool addresses
     */
    function getWETHUSDCPools() external returns (
        address pool005,
        address pool03,
        address pool1
    ) {
        pool005 = FACTORY.getPool(WETH, USDC, 500);   // 0.05%
        pool03 = FACTORY.getPool(WETH, USDC, 3000);   // 0.3%
        pool1 = FACTORY.getPool(WETH, USDC, 10000);   // 1.0%
        
        emit PoolAddressRetrieved(WETH, USDC, 500, pool005);
        emit PoolAddressRetrieved(WETH, USDC, 3000, pool03);
        emit PoolAddressRetrieved(WETH, USDC, 10000, pool1);
        
        console.log("WETH/USDC 0.05% pool:", pool005);
        console.log("WETH/USDC 0.3% pool:", pool03);
        console.log("WETH/USDC 1.0% pool:", pool1);
        
        return (pool005, pool03, pool1);
    }
    
    /**
     * @dev Simple test function
     */
    function testFunction() external pure returns (string memory) {
        return "Test successful";
    }
    
    /**
     * @dev Get contract info
     */
    function getContractInfo() external view returns (
        address contractOwner,
        address factoryAddress,
        address wethAddress,
        address usdcAddress
    ) {
        return (
            owner(),
            address(FACTORY),
            WETH,
            USDC
        );
    }
}
