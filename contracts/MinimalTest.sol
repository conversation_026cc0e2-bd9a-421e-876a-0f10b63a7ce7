// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title MinimalTest
 * @dev Minimal test contract to verify deployment works
 */
contract MinimalTest is Ownable {
    
    string public message;
    uint256 public deploymentTime;
    
    event ContractDeployed(address indexed deployer, uint256 timestamp);
    event MessageSet(string newMessage);
    
    constructor() Ownable(msg.sender) {
        message = "Hello from MinimalTest!";
        deploymentTime = block.timestamp;
        
        emit ContractDeployed(msg.sender, block.timestamp);
    }
    
    function setMessage(string memory newMessage) external onlyOwner {
        message = newMessage;
        emit MessageSet(newMessage);
    }
    
    function getMessage() external view returns (string memory) {
        return message;
    }
    
    function getDeploymentInfo() external view returns (
        address contractOwner,
        uint256 deployTime,
        string memory currentMessage
    ) {
        return (owner(), deploymentTime, message);
    }
}
