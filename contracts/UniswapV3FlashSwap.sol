// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "hardhat/console.sol";

// Uniswap V3 interfaces
interface IUniswapV3Pool {
    function flash(
        address recipient,
        uint256 amount0,
        uint256 amount1,
        bytes calldata data
    ) external;
    
    function token0() external view returns (address);
    function token1() external view returns (address);
    function fee() external view returns (uint24);
    function slot0() external view returns (
        uint160 sqrtPriceX96,
        int24 tick,
        uint16 observationIndex,
        uint16 observationCardinality,
        uint16 observationCardinalityNext,
        uint8 feeProtocol,
        bool unlocked
    );
}

interface IUniswapV3Factory {
    function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params) external payable returns (uint256 amountOut);
}

/**
 * @title UniswapV3FlashSwap
 * @dev Contract for executing flash swaps on Uniswap V3 for fee tier arbitrage
 * @notice This contract performs arbitrage between different fee tiers of the same token pair
 */
contract UniswapV3FlashSwap is Ownable, ReentrancyGuard {

    // Uniswap V3 addresses (mainnet)
    IUniswapV3Factory public constant FACTORY = IUniswapV3Factory(0x1F98431c8aD98523631AE4a59f267346ea31F984);
    IUniswapV3Router public constant ROUTER = IUniswapV3Router(0xE592427A0AEce92De3Edee1F18E0157C05861564);

    // Supported fee tiers
    uint24[] public feeTiers = [500, 3000, 10000]; // 0.05%, 0.3%, 1.0%

    constructor() Ownable(msg.sender) {
        // Constructor sets the deployer as the initial owner
    }
    
    struct FlashSwapParams {
        address tokenA;
        address tokenB;
        uint24 borrowFee;    // Fee tier of pool to borrow from
        uint24 sellFee;      // Fee tier of pool to sell to
        uint256 amount;      // Amount to flash swap
        uint256 minProfit;   // Minimum profit required
        bool isToken0;       // Whether we're borrowing token0 or token1
    }
    
    event FlashSwapExecuted(
        address indexed tokenA,
        address indexed tokenB,
        uint24 borrowFee,
        uint24 sellFee,
        uint256 amount,
        uint256 profit
    );
    
    event ArbitrageOpportunity(
        address indexed tokenA,
        address indexed tokenB,
        uint24 lowFeeTier,
        uint24 highFeeTier,
        uint256 expectedProfit
    );
    
    /**
     * @dev Execute flash swap arbitrage between different fee tiers
     * @param tokenA First token in the pair
     * @param tokenB Second token in the pair
     * @param borrowFee Fee tier to borrow from (usually lower fee = better price)
     * @param sellFee Fee tier to sell to (usually higher fee = worse price)
     * @param amount Amount to flash swap
     * @param minProfit Minimum profit required
     */
    function executeFlashSwapArbitrage(
        address tokenA,
        address tokenB,
        uint24 borrowFee,
        uint24 sellFee,
        uint256 amount,
        uint256 minProfit
    ) external onlyOwner nonReentrant {
        require(tokenA != tokenB, "Tokens must be different");
        require(amount > 0, "Amount must be greater than 0");
        require(borrowFee != sellFee, "Fee tiers must be different");
        
        // Get pool addresses
        address borrowPool = FACTORY.getPool(tokenA, tokenB, borrowFee);
        address sellPool = FACTORY.getPool(tokenA, tokenB, sellFee);
        
        require(borrowPool != address(0), "Borrow pool does not exist");
        require(sellPool != address(0), "Sell pool does not exist");
        
        console.log("=== FLASH SWAP ARBITRAGE STARTED ===");
        console.log("TokenA:", tokenA);
        console.log("TokenB:", tokenB);
        console.log("Borrow Pool:", borrowPool);
        console.log("Sell Pool:", sellPool);
        console.log("Amount:", amount);
        
        // Determine token order in the pool
        address token0 = IUniswapV3Pool(borrowPool).token0();
        bool isToken0 = (tokenA == token0);
        
        // Prepare flash swap parameters
        FlashSwapParams memory params = FlashSwapParams({
            tokenA: tokenA,
            tokenB: tokenB,
            borrowFee: borrowFee,
            sellFee: sellFee,
            amount: amount,
            minProfit: minProfit,
            isToken0: isToken0
        });
        
        // Execute flash swap
        uint256 amount0 = isToken0 ? amount : 0;
        uint256 amount1 = isToken0 ? 0 : amount;
        
        IUniswapV3Pool(borrowPool).flash(
            address(this),
            amount0,
            amount1,
            abi.encode(params)
        );
    }
    
    /**
     * @dev Uniswap V3 flash callback - called by the pool after flash swap
     * @param fee0 Fee for token0
     * @param fee1 Fee for token1
     * @param data Encoded FlashSwapParams
     */
    function uniswapV3FlashCallback(
        uint256 fee0,
        uint256 fee1,
        bytes calldata data
    ) external {
        console.log("=== FLASH CALLBACK STARTED ===");
        console.log("Fee0:", fee0);
        console.log("Fee1:", fee1);
        
        // Decode parameters
        FlashSwapParams memory params = abi.decode(data, (FlashSwapParams));
        
        // Verify the caller is a valid Uniswap V3 pool
        address expectedPool = FACTORY.getPool(params.tokenA, params.tokenB, params.borrowFee);
        require(msg.sender == expectedPool, "Invalid pool caller");
        
        // Record initial balances
        uint256 initialBalanceA = IERC20(params.tokenA).balanceOf(address(this));
        uint256 initialBalanceB = IERC20(params.tokenB).balanceOf(address(this));
        
        console.log("Initial balance A:", initialBalanceA);
        console.log("Initial balance B:", initialBalanceB);
        
        // Execute arbitrage: swap the borrowed token on the other fee tier
        uint256 amountOut = _executeArbitrageSwap(params);
        
        console.log("Amount received from arbitrage:", amountOut);
        
        // Calculate repayment amount (borrowed amount + fee)
        uint256 repayAmount = params.amount + (params.isToken0 ? fee0 : fee1);
        console.log("Repay amount:", repayAmount);
        
        // Check if we have enough to repay and make profit
        uint256 finalBalance = IERC20(params.tokenA).balanceOf(address(this));
        require(finalBalance >= repayAmount, "Insufficient balance to repay flash swap");
        
        uint256 profit = finalBalance - repayAmount;
        require(profit >= params.minProfit, "Insufficient profit");
        
        console.log("Profit:", profit);
        
        // Repay the flash swap
        IERC20(params.tokenA).transfer(msg.sender, repayAmount);
        
        emit FlashSwapExecuted(
            params.tokenA,
            params.tokenB,
            params.borrowFee,
            params.sellFee,
            params.amount,
            profit
        );
        
        console.log("=== FLASH SWAP COMPLETED SUCCESSFULLY ===");
    }
    
    /**
     * @dev Execute the arbitrage swap on the different fee tier
     * @param params Flash swap parameters
     * @return amountOut Amount received from the swap
     */
    function _executeArbitrageSwap(FlashSwapParams memory params) internal returns (uint256 amountOut) {
        // Approve router to spend the borrowed token
        IERC20(params.tokenA).approve(address(ROUTER), params.amount);
        
        // Execute swap: tokenA -> tokenB on sellFee tier
        IUniswapV3Router.ExactInputSingleParams memory swapParams = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: params.tokenA,
            tokenOut: params.tokenB,
            fee: params.sellFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: params.amount,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });
        
        uint256 tokenBReceived = ROUTER.exactInputSingle(swapParams);
        console.log("TokenB received:", tokenBReceived);
        
        // Approve router to spend tokenB
        IERC20(params.tokenB).approve(address(ROUTER), tokenBReceived);
        
        // Execute reverse swap: tokenB -> tokenA on borrowFee tier
        IUniswapV3Router.ExactInputSingleParams memory reverseSwapParams = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: params.tokenB,
            tokenOut: params.tokenA,
            fee: params.borrowFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: tokenBReceived,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });
        
        amountOut = ROUTER.exactInputSingle(reverseSwapParams);
        console.log("TokenA received from reverse swap:", amountOut);
        
        return amountOut;
    }
    
    /**
     * @dev Emergency function to withdraw any stuck tokens
     * @param token Token address to withdraw
     * @param amount Amount to withdraw
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
    
    /**
     * @dev Get pool address for a token pair and fee tier
     * @param tokenA First token
     * @param tokenB Second token
     * @param fee Fee tier
     * @return pool Pool address
     */
    function getPoolAddress(address tokenA, address tokenB, uint24 fee) external view returns (address pool) {
        return FACTORY.getPool(tokenA, tokenB, fee);
    }
    
    /**
     * @dev Check if arbitrage opportunity exists between fee tiers
     * @return exists Whether opportunity exists
     * @return lowFeeTier Lower fee tier
     * @return highFeeTier Higher fee tier
     * @return expectedProfit Expected profit
     */
    function checkArbitrageOpportunity(
        address, /* tokenA */
        address, /* tokenB */
        uint256  /* amount */
    ) external pure returns (
        bool exists,
        uint24 lowFeeTier,
        uint24 highFeeTier,
        uint256 expectedProfit
    ) {
        // This would require complex price calculation logic
        // For now, return false - implement price checking in TypeScript
        return (false, 0, 0, 0);
    }
}
