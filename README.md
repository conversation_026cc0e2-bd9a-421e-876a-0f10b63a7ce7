# Advanced MEV Bot

A sophisticated Maximum Extractable Value (MEV) bot built with TypeScript that implements multiple MEV strategies including sandwich attacks, front-running, arbitrage, and advanced multi-block operations.

## ⚡ Quick Start

### **🧪 Development Testing**
```bash
# 1. Install dependencies
npm install

# 2. Quick Hardhat setup (recommended)
npm run quickstart:hardhat

# 3. Start MEV bot in simulation mode
npm run dev:simulate
```

### **🚀 Production Deployment**
```bash
# 1. Check deployment readiness
npm run check:mainnet

# 2. Deploy with cost protection ($30 max)
npm run deploy:cost-optimized

# 3. Start production trading
npm run start:mainnet
```

### **📋 Available Commands**
- **Quick Reference**: See `QUICK_REFERENCE.md`
- **Full Documentation**: See `docs/NPM_SCRIPTS_DOCUMENTATION.md`
- **37 organized scripts** in 6 categories

## 🚀 Features

### Core MEV Strategies
- **Sandwich Attacks**: Front-run and back-run large swaps for profit
- **Front-Running**: Execute transactions before profitable targets
- **Arbitrage**: Cross-DEX and triangular arbitrage opportunities
- **🔥 Flashloan Arbitrage**: Capital-free arbitrage using Aave flashloans
- **⚡ Uniswap V3 Flash Swaps**: Native V3 flash swaps for fee tier arbitrage
- **Multi-Block Attacks**: Execute strategies across multiple blocks
- **Multi-Pool Arbitrage**: Complex arbitrage across multiple token pairs

### Advanced Capabilities
- **Real-time Mempool Monitoring**: Via Flashbots and ethers.js WebSocket
- **Bundle Simulation**: Flashbots simulation before execution
- **Dynamic Gas Optimization**: Competitive gas pricing strategies
- **Liquidity-Aware Trading**: On-chain pool reserve analysis
- **Dynamic Calldata Generation**: Adaptive transaction encoding
- **Profit Calculation**: Real-time profitability analysis
- **Risk Management**: Slippage protection and emergency stops

### DEX Integrations
- Uniswap V2 & V3
- Balancer (extensible)
- Curve (extensible)
- Dynamic route analysis

### Technical Features
- **Flashbots Integration**: Bundle submission and simulation
- **EIP-1559 Support**: Dynamic gas fee optimization
- **Multi-Block Strategies**: Bundle submission across blocks
- **Automatic Token Approvals**: ERC-20 approval management
- **WETH Wrapping/Unwrapping**: Automatic ETH/WETH handling
- **Comprehensive Logging**: Winston-based logging system
- **Dry Run Mode**: Safe testing without real transactions

## 📁 Project Structure

```
src/
├── index.ts              # Main entry point
├── config/               # Configuration management
├── core/                 # Core MEV bot logic
├── mempool/              # Mempool monitoring
├── dex/                  # DEX integrations and pool management
├── strategies/           # MEV strategies (sandwich, arbitrage)
├── simulation/           # Bundle simulation
├── gas/                  # Gas optimization
├── calldata/             # Transaction encoding/decoding
├── utils/                # Utility functions
└── types/                # TypeScript type definitions
```

## 🛠 Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd bo1
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Build the project**
```bash
npm run build
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Ethereum Network
RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
FLASHBOTS_RPC_URL=https://relay.flashbots.net
CHAIN_ID=1

# Private Keys (NEVER commit real keys)
PRIVATE_KEY=0x...
FLASHBOTS_SIGNER_KEY=0x...

# MEV Configuration
MIN_PROFIT_WEI=1000000000000000000  # 1 ETH minimum profit
MAX_GAS_PRICE_GWEI=100
MAX_PRIORITY_FEE_GWEI=5
SLIPPAGE_TOLERANCE=0.005  # 0.5%

# Strategy Configuration
ENABLE_SANDWICH_ATTACKS=true
ENABLE_FRONT_RUNNING=true
ENABLE_ARBITRAGE=true
ENABLE_MULTI_BLOCK_ATTACKS=false

# Safety
DRY_RUN=true  # Set to false for live trading
SIMULATION_MODE=false  # Set to true for mainnet monitoring without execution
EMERGENCY_STOP=false
```

### Key Configuration Options

- **DRY_RUN**: When `true`, simulates all operations without sending real transactions
- **SIMULATION_MODE**: When `true`, detects real mainnet opportunities but doesn't execute transactions
- **MIN_PROFIT_WEI**: Minimum profit threshold in wei
- **MAX_GAS_PRICE_GWEI**: Maximum gas price to prevent overpaying
- **SLIPPAGE_TOLERANCE**: Maximum acceptable slippage (0.005 = 0.5%)

## 🚀 Usage

### Quick Start
1. **Install dependencies**
```bash
npm install
```

2. **Deploy Flashloan Contract (for flashloan arbitrage)**
```bash
npx hardhat run scripts/deploy-flashloan.js --network sepolia
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your settings
```

3. **Build the project**
```bash
npm run build
```

4. **Run the bot**
```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

### Current Status
This project includes:
- ✅ **Working Simple Bot**: Basic MEV bot that compiles and runs
- ✅ **Complete Architecture**: Full MEV bot framework (advanced features)
- ✅ **Configuration System**: Environment-based configuration
- ✅ **Logging System**: Comprehensive logging with Winston
- ⚠️ **Advanced Features**: Implemented but need TypeScript fixes

### Testing Options

#### 1. Dry Run Mode (Recommended First)
```bash
# Ensure DRY_RUN=true in .env
npm run dev
```

#### 2. Simulation Mode (Mainnet Monitoring)
```bash
# Set SIMULATION_MODE=true and CHAIN_ID=1 in .env
# Detects real opportunities without executing transactions
npm run dev
```

#### 3. Sepolia Testnet (Safe Live Testing)
**Quick Setup** (Recommended):
```bash
# Automated Sepolia setup
npm run setup:sepolia
# Follow the prompts to configure everything
```

**Manual Setup**:
```bash
# Copy pre-configured Sepolia settings
cp .env.example .env
# Edit .env with your Infura key and generated private keys
npm run dev
```

See [SEPOLIA_SETUP.md](docs/SEPOLIA_SETUP.md) for detailed testnet setup guide.

## 🔧 Core Components

### MEVBot (Core)
The main bot orchestrator that coordinates all components:
- Manages mempool monitoring
- Executes MEV strategies
- Handles risk management
- Provides status reporting

### MempoolMonitor
Real-time transaction monitoring:
- WebSocket connection to Ethereum node
- Flashbots mempool integration
- Transaction filtering and analysis
- Event-driven architecture

### Strategies

#### SandwichStrategy
- Analyzes victim transactions for sandwich opportunities
- Calculates optimal front-run and back-run amounts
- Simulates profitability before execution
- Dynamic gas pricing for competitive inclusion

#### ArbitrageStrategy
- Scans for price differences across DEXes
- Implements triangular arbitrage detection
- Calculates optimal trade amounts
- Multi-hop route optimization

#### FlashloanStrategy
- **Capital-free arbitrage** using Aave V3 flashloans
- **Single transaction execution**: Flashloan → Arbitrage → Repay → Profit
- **Smart contract integration**: Deploys dedicated arbitrage contract
- **Risk-optimized**: Higher profit thresholds due to complexity
- **Multi-DEX support**: Arbitrage between Uniswap V2 and V3

**Flashloan Flow:**
1. 💰 Flashloan USDC from Aave
2. 🔄 Execute arbitrage DEX A → DEX B
3. 💸 Repay flashloan with 0.09% premium
4. 💎 Keep remaining profit

See [FLASHLOAN_ARBITRAGE.md](./docs/FLASHLOAN_ARBITRAGE.md) for detailed setup.

### BundleSimulator
- Flashbots bundle simulation
- Profit estimation and validation
- Gas cost calculation
- Risk assessment

### GasOptimizer
- Dynamic gas price optimization
- EIP-1559 fee calculation
- Competitive pricing strategies
- Historical gas analysis

## 📊 Monitoring & Logging

The bot provides comprehensive monitoring:

### Real-time Status
- Total profit/loss
- Success/failure rates
- Gas consumption
- Active opportunities

### Logging Levels
- **ERROR**: Critical issues requiring attention
- **WARN**: Important warnings
- **INFO**: General operational information
- **DEBUG**: Detailed debugging information

### Log Files
- `logs/error.log`: Error-only logs
- `logs/combined.log`: All log levels

## ⚠️ Risk Management

### Built-in Safety Features
- **Emergency Stop**: Immediate halt on critical errors
- **Dry Run Mode**: Test without real transactions
- **Profit Thresholds**: Minimum profit requirements
- **Gas Limits**: Maximum gas price protection
- **Slippage Protection**: Maximum slippage tolerance
- **Balance Monitoring**: Insufficient funds detection

### Best Practices
1. **Start with Dry Run**: Always test with `DRY_RUN=true`
2. **Monitor Closely**: Watch logs and metrics continuously
3. **Set Conservative Limits**: Use reasonable profit thresholds
4. **Regular Updates**: Keep dependencies and strategies updated
5. **Backup Keys**: Secure private key management

## 🔐 Security Considerations

### Private Key Management
- Never commit private keys to version control
- Use environment variables for sensitive data
- Consider hardware wallets for production
- Implement key rotation policies

### Network Security
- Use secure RPC endpoints
- Monitor for unusual activity
- Implement rate limiting
- Use VPN for additional security

## 🧪 Testing

### Simulation Testing
```bash
# Run with DRY_RUN=true
npm run dev
```

### Testnet Testing
```bash
# Configure for testnet in .env
CHAIN_ID=5  # Goerli
RPC_URL=https://goerli.infura.io/v3/YOUR_KEY
```

## 📈 Performance Optimization

### Gas Optimization
- Dynamic gas pricing based on network conditions
- Competitive priority fees for faster inclusion
- Bundle optimization for multiple transactions

### Latency Optimization
- Direct node connections
- Optimized transaction encoding
- Efficient pool data caching

### Profit Maximization
- Real-time opportunity scoring
- Dynamic amount optimization
- Multi-strategy execution

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add comprehensive tests
5. Submit a pull request

## ⚖️ Legal Disclaimer

This software is for educational and research purposes only. MEV extraction operates in a legal gray area and may be subject to regulation. Users are responsible for:

- Compliance with local laws and regulations
- Understanding the risks involved
- Proper risk management
- Ethical considerations

The authors are not responsible for any financial losses or legal issues arising from the use of this software.

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the logs for error messages
2. Review the configuration
3. Consult the documentation
4. Open an issue on GitHub

---

**⚠️ WARNING**: MEV extraction involves significant financial risk. Never use this software with funds you cannot afford to lose. Always test thoroughly in dry-run mode before live deployment.
