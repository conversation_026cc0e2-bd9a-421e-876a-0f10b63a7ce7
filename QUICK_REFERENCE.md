# 🚀 MEV Bot Quick Reference

## ⚡ **MOST USED COMMANDS**

### **🏃 Quick Start**
```bash
npm run dev                    # Start MEV bot (recommended)
npm run quickstart:hardhat     # Complete Hardhat setup
npm run dev:simulate           # Safe simulation mode
```

### **🚀 Production Deployment**
```bash
npm run check:mainnet          # Check deployment readiness
npm run deploy:cost-optimized  # Deploy with $30 cost protection
npm run start:mainnet          # Start production bot
```

### **🧪 Testing**
```bash
npm run test:all-strategies    # Test all MEV strategies
npm run test:ultimate-mev      # Comprehensive strategy testing
npm run hardhat:deploy         # Deploy to Hardhat
```

### **⛽ Gas Monitoring**
```bash
npm run monitor:gas            # Real-time gas price monitoring
```

---

## 📋 **WORKFLOW CHEATSHEET**

### **Development Workflow**
```bash
# 1. Set up testing environment
npm run quickstart:hardhat

# 2. Start development
npm run dev:hardhat

# 3. Test strategies
npm run test:all-strategies
```

### **Production Workflow**
```bash
# 1. Validate readiness
npm run check:mainnet

# 2. Deploy contracts
npm run deploy:cost-optimized

# 3. Start trading
npm run start:mainnet
```

### **Testing Workflow**
```bash
# 1. Generate opportunities
npm run generate:mev-opportunities

# 2. Test specific strategy
npm run test:sandwich

# 3. Test all strategies
npm run test:ultimate-mev
```

---

## 🔧 **ENVIRONMENT COMMANDS**

| Environment | Start Command | Use Case |
|-------------|---------------|----------|
| **Development** | `npm run dev` | Main development |
| **Simulation** | `npm run dev:simulate` | Safe testing |
| **Hardhat** | `npm run dev:hardhat` | Local testing |
| **Production** | `npm run start:mainnet` | Live trading |

---

## 💰 **COST PROTECTION**

| Command | Max Cost | Protection |
|---------|----------|------------|
| `deploy:cost-optimized` | $30 USD | ✅ Full protection |
| `deploy:mainnet` | No limit | ❌ No protection |
| `monitor:gas` | N/A | 📊 Monitoring only |

---

## 🎯 **TESTING COMMANDS**

| Test Type | Command | Purpose |
|-----------|---------|---------|
| **All Strategies** | `test:all-strategies` | Complete testing |
| **Flashloan** | `test:flashloan` | Flashloan attacks |
| **Sandwich** | `test:sandwich` | Sandwich attacks |
| **Frontrunning** | `test:frontrunning` | Frontrunning |
| **Atomic** | `test:atomic` | Atomic execution |

---

## 🚨 **EMERGENCY COMMANDS**

```bash
# Stop all processes
pkill -f "ts-node"
pkill -f "hardhat node"

# Reset environment
npm run clean
npm run build

# Check system status
npm run check:mainnet
npm run verify:production
```

---

## 📊 **STATUS INDICATORS**

### **Ready to Deploy** ✅
- `npm run check:mainnet` passes
- Account has ≥ 0.1 ETH
- Gas prices acceptable

### **Not Ready** ❌
- Insufficient balance
- Network issues
- Gas prices too high

### **Testing Ready** 🧪
- Hardhat node running
- Contracts deployed
- Accounts funded

---

## 🔗 **QUICK LINKS**

- **Full Documentation**: `docs/NPM_SCRIPTS_DOCUMENTATION.md`
- **Environment Setup**: `docs/ENVIRONMENT_VARIABLES.md`
- **Production Guide**: `docs/PRODUCTION_DEPLOYMENT_GUIDE.md`
- **Cost Optimization**: `COST_OPTIMIZED_DEPLOYMENT.md`

---

**💡 Tip**: Always start with `npm run dev:simulate` for safe testing!
