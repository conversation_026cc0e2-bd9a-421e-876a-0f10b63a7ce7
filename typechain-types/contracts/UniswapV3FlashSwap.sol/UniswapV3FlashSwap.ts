/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface UniswapV3FlashSwapInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "FACTORY"
      | "ROUTER"
      | "checkArbitrageOpportunity"
      | "emergencyWithdraw"
      | "executeFlashSwapArbitrage"
      | "feeTiers"
      | "getPoolAddress"
      | "owner"
      | "renounceOwnership"
      | "transferOwnership"
      | "uniswapV3FlashCallback"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ArbitrageOpportunity"
      | "FlashSwapExecuted"
      | "OwnershipTransferred"
  ): EventFragment;

  encodeFunctionData(functionFragment: "FACTORY", values?: undefined): string;
  encodeFunctionData(functionFragment: "ROUTER", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "checkArbitrageOpportunity",
    values: [AddressLike, AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "executeFlashSwapArbitrage",
    values: [
      AddressLike,
      AddressLike,
      BigNumberish,
      BigNumberish,
      BigNumberish,
      BigNumberish
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "feeTiers",
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "getPoolAddress",
    values: [AddressLike, AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "uniswapV3FlashCallback",
    values: [BigNumberish, BigNumberish, BytesLike]
  ): string;

  decodeFunctionResult(functionFragment: "FACTORY", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "ROUTER", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "checkArbitrageOpportunity",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeFlashSwapArbitrage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "feeTiers", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getPoolAddress",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "uniswapV3FlashCallback",
    data: BytesLike
  ): Result;
}

export namespace ArbitrageOpportunityEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    lowFeeTier: BigNumberish,
    highFeeTier: BigNumberish,
    expectedProfit: BigNumberish
  ];
  export type OutputTuple = [
    tokenA: string,
    tokenB: string,
    lowFeeTier: bigint,
    highFeeTier: bigint,
    expectedProfit: bigint
  ];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    lowFeeTier: bigint;
    highFeeTier: bigint;
    expectedProfit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace FlashSwapExecutedEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    borrowFee: BigNumberish,
    sellFee: BigNumberish,
    amount: BigNumberish,
    profit: BigNumberish
  ];
  export type OutputTuple = [
    tokenA: string,
    tokenB: string,
    borrowFee: bigint,
    sellFee: bigint,
    amount: bigint,
    profit: bigint
  ];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    borrowFee: bigint;
    sellFee: bigint;
    amount: bigint;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface UniswapV3FlashSwap extends BaseContract {
  connect(runner?: ContractRunner | null): UniswapV3FlashSwap;
  waitForDeployment(): Promise<this>;

  interface: UniswapV3FlashSwapInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  FACTORY: TypedContractMethod<[], [string], "view">;

  ROUTER: TypedContractMethod<[], [string], "view">;

  checkArbitrageOpportunity: TypedContractMethod<
    [arg0: AddressLike, arg1: AddressLike, arg2: BigNumberish],
    [
      [boolean, bigint, bigint, bigint] & {
        exists: boolean;
        lowFeeTier: bigint;
        highFeeTier: bigint;
        expectedProfit: bigint;
      }
    ],
    "view"
  >;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;

  executeFlashSwapArbitrage: TypedContractMethod<
    [
      tokenA: AddressLike,
      tokenB: AddressLike,
      borrowFee: BigNumberish,
      sellFee: BigNumberish,
      amount: BigNumberish,
      minProfit: BigNumberish
    ],
    [void],
    "nonpayable"
  >;

  feeTiers: TypedContractMethod<[arg0: BigNumberish], [bigint], "view">;

  getPoolAddress: TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike, fee: BigNumberish],
    [string],
    "view"
  >;

  owner: TypedContractMethod<[], [string], "view">;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  uniswapV3FlashCallback: TypedContractMethod<
    [fee0: BigNumberish, fee1: BigNumberish, data: BytesLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "FACTORY"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "checkArbitrageOpportunity"
  ): TypedContractMethod<
    [arg0: AddressLike, arg1: AddressLike, arg2: BigNumberish],
    [
      [boolean, bigint, bigint, bigint] & {
        exists: boolean;
        lowFeeTier: bigint;
        highFeeTier: bigint;
        expectedProfit: bigint;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "executeFlashSwapArbitrage"
  ): TypedContractMethod<
    [
      tokenA: AddressLike,
      tokenB: AddressLike,
      borrowFee: BigNumberish,
      sellFee: BigNumberish,
      amount: BigNumberish,
      minProfit: BigNumberish
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "feeTiers"
  ): TypedContractMethod<[arg0: BigNumberish], [bigint], "view">;
  getFunction(
    nameOrSignature: "getPoolAddress"
  ): TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike, fee: BigNumberish],
    [string],
    "view"
  >;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "uniswapV3FlashCallback"
  ): TypedContractMethod<
    [fee0: BigNumberish, fee1: BigNumberish, data: BytesLike],
    [void],
    "nonpayable"
  >;

  getEvent(
    key: "ArbitrageOpportunity"
  ): TypedContractEvent<
    ArbitrageOpportunityEvent.InputTuple,
    ArbitrageOpportunityEvent.OutputTuple,
    ArbitrageOpportunityEvent.OutputObject
  >;
  getEvent(
    key: "FlashSwapExecuted"
  ): TypedContractEvent<
    FlashSwapExecutedEvent.InputTuple,
    FlashSwapExecutedEvent.OutputTuple,
    FlashSwapExecutedEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;

  filters: {
    "ArbitrageOpportunity(address,address,uint24,uint24,uint256)": TypedContractEvent<
      ArbitrageOpportunityEvent.InputTuple,
      ArbitrageOpportunityEvent.OutputTuple,
      ArbitrageOpportunityEvent.OutputObject
    >;
    ArbitrageOpportunity: TypedContractEvent<
      ArbitrageOpportunityEvent.InputTuple,
      ArbitrageOpportunityEvent.OutputTuple,
      ArbitrageOpportunityEvent.OutputObject
    >;

    "FlashSwapExecuted(address,address,uint24,uint24,uint256,uint256)": TypedContractEvent<
      FlashSwapExecutedEvent.InputTuple,
      FlashSwapExecutedEvent.OutputTuple,
      FlashSwapExecutedEvent.OutputObject
    >;
    FlashSwapExecuted: TypedContractEvent<
      FlashSwapExecutedEvent.InputTuple,
      FlashSwapExecutedEvent.OutputTuple,
      FlashSwapExecutedEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
  };
}
