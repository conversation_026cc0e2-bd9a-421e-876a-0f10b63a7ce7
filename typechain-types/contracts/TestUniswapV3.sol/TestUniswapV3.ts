/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface TestUniswapV3Interface extends Interface {
  getFunction(
    nameOrSignature:
      | "FACTORY"
      | "USDC"
      | "WETH"
      | "getContractInfo"
      | "getPoolAddress"
      | "getWETHUSDCPools"
      | "owner"
      | "renounceOwnership"
      | "testFunction"
      | "transferOwnership"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ContractDeployed"
      | "OwnershipTransferred"
      | "PoolAddressRetrieved"
  ): EventFragment;

  encodeFunctionData(functionFragment: "FACTORY", values?: undefined): string;
  encodeFunctionData(functionFragment: "USDC", values?: undefined): string;
  encodeFunctionData(functionFragment: "WETH", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "getContractInfo",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getPoolAddress",
    values: [AddressLike, AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "getWETHUSDCPools",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "testFunction",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;

  decodeFunctionResult(functionFragment: "FACTORY", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "USDC", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "WETH", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getContractInfo",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getPoolAddress",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getWETHUSDCPools",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "testFunction",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
}

export namespace ContractDeployedEvent {
  export type InputTuple = [deployer: AddressLike, timestamp: BigNumberish];
  export type OutputTuple = [deployer: string, timestamp: bigint];
  export interface OutputObject {
    deployer: string;
    timestamp: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace PoolAddressRetrievedEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    fee: BigNumberish,
    pool: AddressLike
  ];
  export type OutputTuple = [
    tokenA: string,
    tokenB: string,
    fee: bigint,
    pool: string
  ];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    fee: bigint;
    pool: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface TestUniswapV3 extends BaseContract {
  connect(runner?: ContractRunner | null): TestUniswapV3;
  waitForDeployment(): Promise<this>;

  interface: TestUniswapV3Interface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  FACTORY: TypedContractMethod<[], [string], "view">;

  USDC: TypedContractMethod<[], [string], "view">;

  WETH: TypedContractMethod<[], [string], "view">;

  getContractInfo: TypedContractMethod<
    [],
    [
      [string, string, string, string] & {
        contractOwner: string;
        factoryAddress: string;
        wethAddress: string;
        usdcAddress: string;
      }
    ],
    "view"
  >;

  getPoolAddress: TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike, fee: BigNumberish],
    [string],
    "view"
  >;

  getWETHUSDCPools: TypedContractMethod<
    [],
    [
      [string, string, string] & {
        pool005: string;
        pool03: string;
        pool1: string;
      }
    ],
    "nonpayable"
  >;

  owner: TypedContractMethod<[], [string], "view">;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  testFunction: TypedContractMethod<[], [string], "view">;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "FACTORY"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "USDC"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "WETH"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "getContractInfo"
  ): TypedContractMethod<
    [],
    [
      [string, string, string, string] & {
        contractOwner: string;
        factoryAddress: string;
        wethAddress: string;
        usdcAddress: string;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "getPoolAddress"
  ): TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike, fee: BigNumberish],
    [string],
    "view"
  >;
  getFunction(
    nameOrSignature: "getWETHUSDCPools"
  ): TypedContractMethod<
    [],
    [
      [string, string, string] & {
        pool005: string;
        pool03: string;
        pool1: string;
      }
    ],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "testFunction"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;

  getEvent(
    key: "ContractDeployed"
  ): TypedContractEvent<
    ContractDeployedEvent.InputTuple,
    ContractDeployedEvent.OutputTuple,
    ContractDeployedEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;
  getEvent(
    key: "PoolAddressRetrieved"
  ): TypedContractEvent<
    PoolAddressRetrievedEvent.InputTuple,
    PoolAddressRetrievedEvent.OutputTuple,
    PoolAddressRetrievedEvent.OutputObject
  >;

  filters: {
    "ContractDeployed(address,uint256)": TypedContractEvent<
      ContractDeployedEvent.InputTuple,
      ContractDeployedEvent.OutputTuple,
      ContractDeployedEvent.OutputObject
    >;
    ContractDeployed: TypedContractEvent<
      ContractDeployedEvent.InputTuple,
      ContractDeployedEvent.OutputTuple,
      ContractDeployedEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;

    "PoolAddressRetrieved(address,address,uint24,address)": TypedContractEvent<
      PoolAddressRetrievedEvent.InputTuple,
      PoolAddressRetrievedEvent.OutputTuple,
      PoolAddressRetrievedEvent.OutputObject
    >;
    PoolAddressRetrieved: TypedContractEvent<
      PoolAddressRetrievedEvent.InputTuple,
      PoolAddressRetrievedEvent.OutputTuple,
      PoolAddressRetrievedEvent.OutputObject
    >;
  };
}
