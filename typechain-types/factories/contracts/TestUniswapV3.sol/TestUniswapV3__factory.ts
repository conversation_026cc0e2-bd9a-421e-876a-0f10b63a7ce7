/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  TestUniswapV3,
  TestUniswapV3Interface,
} from "../../../contracts/TestUniswapV3.sol/TestUniswapV3";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "deployer",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "timestamp",
        type: "uint256",
      },
    ],
    name: "ContractDeployed",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "fee",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "address",
        name: "pool",
        type: "address",
      },
    ],
    name: "PoolAddressRetrieved",
    type: "event",
  },
  {
    inputs: [],
    name: "FACTORY",
    outputs: [
      {
        internalType: "contract IUniswapV3Factory",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "USDC",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "WETH",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getContractInfo",
    outputs: [
      {
        internalType: "address",
        name: "contractOwner",
        type: "address",
      },
      {
        internalType: "address",
        name: "factoryAddress",
        type: "address",
      },
      {
        internalType: "address",
        name: "wethAddress",
        type: "address",
      },
      {
        internalType: "address",
        name: "usdcAddress",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "fee",
        type: "uint24",
      },
    ],
    name: "getPoolAddress",
    outputs: [
      {
        internalType: "address",
        name: "pool",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getWETHUSDCPools",
    outputs: [
      {
        internalType: "address",
        name: "pool005",
        type: "address",
      },
      {
        internalType: "address",
        name: "pool03",
        type: "address",
      },
      {
        internalType: "address",
        name: "pool1",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "testFunction",
    outputs: [
      {
        internalType: "string",
        name: "",
        type: "string",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type TestUniswapV3ConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestUniswapV3ConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestUniswapV3__factory extends ContractFactory {
  constructor(...args: TestUniswapV3ConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      TestUniswapV3 & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): TestUniswapV3__factory {
    return super.connect(runner) as TestUniswapV3__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestUniswapV3Interface {
    return new Interface(_abi) as TestUniswapV3Interface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): TestUniswapV3 {
    return new Contract(address, _abi, runner) as unknown as TestUniswapV3;
  }
}
