/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  UniswapV3FlashSwap,
  UniswapV3FlashSwapInterface,
} from "../../../contracts/UniswapV3FlashSwap.sol/UniswapV3FlashSwap";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    inputs: [],
    name: "ReentrancyGuardReentrantCall",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "lowFeeTier",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "highFeeTier",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "expectedProfit",
        type: "uint256",
      },
    ],
    name: "ArbitrageOpportunity",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "borrowFee",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "sellFee",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "FlashSwapExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "FACTORY",
    outputs: [
      {
        internalType: "contract IUniswapV3Factory",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "ROUTER",
    outputs: [
      {
        internalType: "contract IUniswapV3Router",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    name: "checkArbitrageOpportunity",
    outputs: [
      {
        internalType: "bool",
        name: "exists",
        type: "bool",
      },
      {
        internalType: "uint24",
        name: "lowFeeTier",
        type: "uint24",
      },
      {
        internalType: "uint24",
        name: "highFeeTier",
        type: "uint24",
      },
      {
        internalType: "uint256",
        name: "expectedProfit",
        type: "uint256",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "borrowFee",
        type: "uint24",
      },
      {
        internalType: "uint24",
        name: "sellFee",
        type: "uint24",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "minProfit",
        type: "uint256",
      },
    ],
    name: "executeFlashSwapArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    name: "feeTiers",
    outputs: [
      {
        internalType: "uint24",
        name: "",
        type: "uint24",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "fee",
        type: "uint24",
      },
    ],
    name: "getPoolAddress",
    outputs: [
      {
        internalType: "address",
        name: "pool",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "fee0",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "fee1",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "data",
        type: "bytes",
      },
    ],
    name: "uniswapV3FlashCallback",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type UniswapV3FlashSwapConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: UniswapV3FlashSwapConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class UniswapV3FlashSwap__factory extends ContractFactory {
  constructor(...args: UniswapV3FlashSwapConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      UniswapV3FlashSwap & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): UniswapV3FlashSwap__factory {
    return super.connect(runner) as UniswapV3FlashSwap__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): UniswapV3FlashSwapInterface {
    return new Interface(_abi) as UniswapV3FlashSwapInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): UniswapV3FlashSwap {
    return new Contract(address, _abi, runner) as unknown as UniswapV3FlashSwap;
  }
}
