/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IUniswapV3Factory,
  IUniswapV3FactoryInterface,
} from "../../../contracts/SimpleUniswapV3FlashSwap.sol/IUniswapV3Factory";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "fee",
        type: "uint24",
      },
    ],
    name: "getPool",
    outputs: [
      {
        internalType: "address",
        name: "pool",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IUniswapV3Factory__factory {
  static readonly abi = _abi;
  static createInterface(): IUniswapV3FactoryInterface {
    return new Interface(_abi) as IUniswapV3FactoryInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IUniswapV3Factory {
    return new Contract(address, _abi, runner) as unknown as IUniswapV3Factory;
  }
}
