/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  SimpleUniswapV3FlashSwap,
  SimpleUniswapV3FlashSwapInterface,
} from "../../../contracts/SimpleUniswapV3FlashSwap.sol/SimpleUniswapV3FlashSwap";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "borrowFee",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "uint24",
        name: "sellFee",
        type: "uint24",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "FlashSwapExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "FACTORY",
    outputs: [
      {
        internalType: "contract IUniswapV3Factory",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "ROUTER",
    outputs: [
      {
        internalType: "contract IUniswapV3Router",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "borrowFee",
        type: "uint24",
      },
      {
        internalType: "uint24",
        name: "sellFee",
        type: "uint24",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "minProfit",
        type: "uint256",
      },
    ],
    name: "executeFlashSwapArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "fee",
        type: "uint24",
      },
    ],
    name: "getPoolAddress",
    outputs: [
      {
        internalType: "address",
        name: "pool",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "fee0",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "fee1",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "data",
        type: "bytes",
      },
    ],
    name: "uniswapV3FlashCallback",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type SimpleUniswapV3FlashSwapConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: SimpleUniswapV3FlashSwapConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class SimpleUniswapV3FlashSwap__factory extends ContractFactory {
  constructor(...args: SimpleUniswapV3FlashSwapConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      SimpleUniswapV3FlashSwap & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): SimpleUniswapV3FlashSwap__factory {
    return super.connect(runner) as SimpleUniswapV3FlashSwap__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): SimpleUniswapV3FlashSwapInterface {
    return new Interface(_abi) as SimpleUniswapV3FlashSwapInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): SimpleUniswapV3FlashSwap {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as SimpleUniswapV3FlashSwap;
  }
}
