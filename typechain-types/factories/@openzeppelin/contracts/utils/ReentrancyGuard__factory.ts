/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  ReentrancyGuard,
  ReentrancyGuardInterface,
} from "../../../../@openzeppelin/contracts/utils/ReentrancyGuard";

const _abi = [
  {
    inputs: [],
    name: "ReentrancyGuardReentrantCall",
    type: "error",
  },
] as const;

export class ReentrancyGuard__factory {
  static readonly abi = _abi;
  static createInterface(): ReentrancyGuardInterface {
    return new Interface(_abi) as ReentrancyGuardInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): ReentrancyGuard {
    return new Contract(address, _abi, runner) as unknown as ReentrancyGuard;
  }
}
