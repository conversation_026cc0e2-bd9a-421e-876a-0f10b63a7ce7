# 📋 NPM Scripts Documentation

## 🎯 **OVERVIEW**
This document provides comprehensive documentation for all available npm scripts in the MEV bot project. Scripts are organized by category for easy navigation.

---

## 🏗️ **BUILD & DEVELOPMENT**

### **Core Development Commands**

#### `npm run build`
**Purpose**: Compile TypeScript to JavaScript
**Output**: `dist/` directory with compiled files
**Usage**: Required before running `npm start`
```bash
npm run build
```

#### `npm run clean`
**Purpose**: Remove compiled files
**Usage**: Clean build artifacts
```bash
npm run clean
```

#### `npm run start`
**Purpose**: Run compiled JavaScript version
**Prerequisites**: Must run `npm run build` first
```bash
npm run build && npm run start
```

### **Development Modes**

#### `npm run dev` ⭐ **RECOMMENDED**
**Purpose**: Start MEV bot with split-screen dashboard
**Features**: 
- Live TypeScript compilation
- Split-screen interface (status + logs)
- Real-time monitoring
```bash
npm run dev
```

#### `npm run dev:watch`
**Purpose**: Development with auto-restart on file changes
**Features**: 
- Nodemon auto-restart
- Split-screen dashboard
- File watching
```bash
npm run dev:watch
```

#### `npm run dev:simulate`
**Purpose**: Run in simulation mode (no real transactions)
**Features**:
- Simulation mode enabled
- Mainnet chain ID
- Split-screen dashboard
- Safe testing environment
```bash
npm run dev:simulate
```

#### `npm run dev:hardhat`
**Purpose**: Run with Hardhat environment configuration
**Prerequisites**: Hardhat node must be running
```bash
npm run dev:hardhat
```

#### `npm run dev:local`
**Purpose**: Run with local node configuration
**Prerequisites**: Local ETH node must be running
```bash
npm run dev:local
```

---

## 🚀 **PRODUCTION DEPLOYMENT**

### **Pre-Deployment Checks**

#### `npm run check:mainnet` ⭐ **REQUIRED FIRST**
**Purpose**: Validate deployment readiness
**Checks**:
- Network connection (mainnet)
- Account balance (minimum 0.1 ETH)
- Current gas prices
- Estimated deployment cost
```bash
npm run check:mainnet
```

#### `npm run monitor:gas`
**Purpose**: Real-time gas price monitoring
**Features**:
- Live gas price tracking
- Cost calculation in USD
- Visual alerts when price ≤ $30
- Best price tracking
```bash
npm run monitor:gas
```

### **Deployment Commands**

#### `npm run deploy:cost-optimized` ⭐ **RECOMMENDED**
**Purpose**: Deploy with automatic cost protection
**Features**:
- Maximum cost limit: $30 USD
- Multi-source gas price analysis
- Automatic cancellation if over budget
- Smart timing optimization
```bash
npm run deploy:cost-optimized
```

#### `npm run deploy:mainnet`
**Purpose**: Standard mainnet deployment
**Features**: Basic deployment without cost protection
```bash
npm run deploy:mainnet
```

#### `npm run deploy:mainnet-production`
**Purpose**: Full production deployment with safety checks
**Features**:
- Comprehensive safety checks
- Interactive confirmations
- Automated environment setup
```bash
npm run deploy:mainnet-production
```

#### `npm run start:mainnet`
**Purpose**: Start MEV bot on mainnet
**Prerequisites**: Contract must be deployed
```bash
npm run start:mainnet
```

---

## 🧪 **HARDHAT TESTING**

### **Node Management**

#### `npm run compile`
**Purpose**: Compile smart contracts
```bash
npm run compile
```

#### `npm run hardhat:node`
**Purpose**: Start isolated Hardhat node
**Features**: 20 accounts with 10,000 ETH each
```bash
npm run hardhat:node
```

#### `npm run hardhat:fork:mainnet`
**Purpose**: Start Hardhat node forking mainnet
**Prerequisites**: Mainnet RPC URL configured
```bash
npm run hardhat:fork:mainnet
```

#### `npm run hardhat:fork:sepolia`
**Purpose**: Start Hardhat node forking Sepolia testnet
```bash
npm run hardhat:fork:sepolia
```

#### `npm run hardhat:fork:local`
**Purpose**: Fork from your local ETH node
**Prerequisites**: Local node running on port 8545
```bash
npm run hardhat:fork:local
```

### **Setup & Configuration**

#### `npm run quickstart:hardhat` ⭐ **QUICK START**
**Purpose**: Complete Hardhat setup in one command
**Features**:
- Starts Hardhat node
- Deploys contracts
- Funds accounts
- Configures environment
```bash
npm run quickstart:hardhat
```

#### `npm run hardhat:setup`
**Purpose**: Configure Hardhat fork environment
```bash
npm run hardhat:setup
```

#### `npm run hardhat:deploy`
**Purpose**: Deploy contracts to Hardhat network
```bash
npm run hardhat:deploy
```

#### `npm run hardhat:fund`
**Purpose**: Fund Hardhat accounts with test tokens
```bash
npm run hardhat:fund
```

---

## ✅ **TESTING & VALIDATION**

### **Integration Testing**

#### `npm run test:hardhat`
**Purpose**: Run Hardhat integration tests
**Prerequisites**: Hardhat node running
```bash
npm run test:hardhat
```

#### `npm run test:flashloan`
**Purpose**: Test flashloan attack functionality
```bash
npm run test:flashloan
```

#### `npm run test:atomic`
**Purpose**: Test atomic transaction execution
```bash
npm run test:atomic
```

#### `npm run test:atomic-success`
**Purpose**: Test successful atomic flashloan
```bash
npm run test:atomic-success
```

#### `npm run demo:atomic`
**Purpose**: Demonstrate atomic MEV execution
```bash
npm run demo:atomic
```

#### `npm run verify:production`
**Purpose**: Final production readiness verification
```bash
npm run verify:production
```

---

## 🎯 **MEV STRATEGY TESTING**

### **Opportunity Generation**

#### `npm run generate:mev-opportunities`
**Purpose**: Generate test MEV opportunities
**Features**: Creates realistic transactions for testing
```bash
npm run generate:mev-opportunities
```

#### `npm run generate:transactions`
**Purpose**: Generate test transactions
```bash
npm run generate:transactions
```

### **Strategy Testing**

#### `npm run test:ultimate-mev` ⭐ **COMPREHENSIVE**
**Purpose**: Test all MEV strategies with profit verification
**Features**:
- Tests all strategy types
- Profit verification
- Success rate monitoring
```bash
npm run test:ultimate-mev
```

#### `npm run test:sandwich`
**Purpose**: Test sandwich attack strategy
```bash
npm run test:sandwich
```

#### `npm run test:frontrunning`
**Purpose**: Test frontrunning strategy
```bash
npm run test:frontrunning
```

#### `npm run test:all-strategies` ⭐ **COMPLETE SUITE**
**Purpose**: Generate opportunities and test all strategies
**Features**: Complete end-to-end testing
```bash
npm run test:all-strategies
```

---

## 🔧 **SETUP & UTILITIES**

### **Environment Setup**

#### `npm run setup:sepolia`
**Purpose**: Set up Sepolia testnet environment
```bash
npm run setup:sepolia
```

#### `npm run setup:local`
**Purpose**: Set up local node environment
```bash
npm run setup:local
```

---

## 🚀 **QUICK START WORKFLOWS**

### **For Development Testing**
```bash
# 1. Quick Hardhat setup
npm run quickstart:hardhat

# 2. Start MEV bot
npm run dev:hardhat

# 3. Test strategies
npm run test:all-strategies
```

### **For Production Deployment**
```bash
# 1. Check readiness
npm run check:mainnet

# 2. Monitor gas prices (optional)
npm run monitor:gas

# 3. Deploy with cost protection
npm run deploy:cost-optimized

# 4. Start production bot
npm run start:mainnet
```

### **For Strategy Development**
```bash
# 1. Start Hardhat
npm run hardhat:fork:mainnet

# 2. Deploy contracts
npm run hardhat:deploy

# 3. Fund accounts
npm run hardhat:fund

# 4. Generate opportunities
npm run generate:mev-opportunities

# 5. Test strategies
npm run test:ultimate-mev
```

---

## ⚠️ **IMPORTANT NOTES**

### **Prerequisites**
- **Node.js**: Version 16+ required
- **Local ETH Node**: For mainnet operations
- **Funded Account**: 0.5 ETH recommended for deployment
- **Environment Variables**: Properly configured `.env` file

### **Safety Reminders**
- Always use `npm run dev:simulate` for initial testing
- Run `npm run check:mainnet` before any mainnet deployment
- Use `npm run deploy:cost-optimized` to protect against high gas costs
- Test thoroughly on Hardhat before mainnet deployment

### **Common Issues**
- **"Insufficient balance"**: Fund your account with ETH
- **"Network not found"**: Check RPC URL configuration
- **"Contract not deployed"**: Run deployment scripts first
- **"Gas price too high"**: Use `npm run monitor:gas` to wait for better prices

---

## 📞 **SUPPORT**

For issues with specific scripts:
1. Check the prerequisites listed above
2. Verify your `.env` configuration
3. Review the error messages carefully
4. Try the simulation mode first (`npm run dev:simulate`)

**Happy MEV hunting! 🎯**
