# 🎭 Simulation Mode Guide

## Overview

Simulation Mode allows you to run the MEV bot on **real mainnet** while detecting and analyzing opportunities **without executing any transactions**. This is perfect for:

- **Monitoring mainnet opportunities** without spending gas
- **Testing strategies** on live data
- **Learning and analysis** without financial risk
- **Opportunity validation** before going live

## Key Features

### ✅ What Simulation Mode Does
- **Detects real opportunities** on mainnet
- **Analyzes profitability** with current gas prices
- **Simulates transaction execution** steps
- **Logs detailed opportunity information**
- **Calculates potential profits** and gas costs
- **Tests all MEV strategies** (sandwich, arbitrage, flashloan, multi-block)

### ❌ What Simulation Mode Doesn't Do
- **No real transactions** are sent to the blockchain
- **No gas fees** are spent
- **No funds** are moved or risked
- **No actual profits** are earned

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# Enable simulation mode
SIMULATION_MODE=true

# Run on mainnet for real opportunities
CHAIN_ID=1
RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY

# Keep dry run disabled when using simulation mode
DRY_RUN=false
```

### Difference from Dry Run

| Feature | DRY_RUN | SIMULATION_MODE |
|---------|---------|-----------------|
| **Purpose** | Testing on testnet | Mainnet monitoring |
| **Network** | Usually testnet | Real mainnet |
| **Opportunities** | Mock/limited | Real live opportunities |
| **Gas Prices** | Testnet prices | Real mainnet gas |
| **Logging** | Basic simulation | Detailed analysis |
| **Use Case** | Development | Production monitoring |

## Usage Examples

### 1. Basic Simulation Mode

```bash
# Set environment variables
export SIMULATION_MODE=true
export CHAIN_ID=1
export RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY

# Run the bot
npm run dev
```

### 2. Mainnet Monitoring Setup

```env
# .env file for mainnet simulation
CHAIN_ID=1
RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY
MEMPOOL_WEBSOCKET_URL=wss://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY

# Enable simulation mode
SIMULATION_MODE=true
DRY_RUN=false

# Configure strategies to monitor
ENABLE_SANDWICH_ATTACKS=true
ENABLE_ARBITRAGE=true
ENABLE_FLASHLOAN_ATTACKS=true
ENABLE_MULTI_BLOCK_ATTACKS=true

# Set realistic profit thresholds
MIN_PROFIT_WEI=50000000000000000  # 0.05 ETH
```

### 3. Switching to Real Execution

When you're ready to execute real transactions:

```env
# Disable simulation mode
SIMULATION_MODE=false

# Keep other settings the same
CHAIN_ID=1
RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY
```

## Expected Output

### Simulation Mode Logs

```
🎭 SIMULATION MODE ENABLED
   Opportunities will be detected and analyzed
   No real transactions will be executed
   Perfect for mainnet monitoring without gas costs

🎭 SIMULATION: Sandwich attack opportunity detected
   Victim Transaction: 0x1234...
   Front-run Amount: 2.5 ETH
   Back-run Amount: 2.7 ETH
   Estimated Profit: 0.15 ETH
   Gas Used: 180000
   Strategy: Would execute via Flashbots bundle

🎭 SIMULATION: Flashloan opportunity detected
   Flashloan Amount: 10000 USDC
   Expected Profit: 0.08 ETH
   Arbitrage Route: USDC → WETH → USDC
   Estimated Gas Cost: 0.02 ETH
   Net Profit: 0.06 ETH
✅ SIMULATION: Flashloan would be profitable and executed
```

### Dashboard Display

The split-screen dashboard will show:
- **Left Panel**: Live status with simulation indicators
- **Right Panel**: Detailed opportunity logs
- **Profit Tracking**: Simulated profits (not real)
- **Gas Analysis**: Real mainnet gas cost estimates

## Best Practices

### 1. RPC Provider Selection
- Use **high-quality RPC providers** (Alchemy, QuickNode)
- Avoid **rate-limited providers** for mainnet monitoring
- Consider **running your own node** for best performance

### 2. Monitoring Strategy
- Start with **higher profit thresholds** to reduce noise
- Monitor **multiple DEX pairs** for more opportunities
- Use **realistic gas price settings** for accurate analysis

### 3. Analysis and Learning
- **Study the logs** to understand MEV patterns
- **Track opportunity frequency** by strategy type
- **Analyze gas cost impacts** on profitability

### 4. Transition to Live Trading
- **Validate strategies** thoroughly in simulation
- **Start with small amounts** when going live
- **Monitor performance** closely after switching

## Troubleshooting

### Common Issues

1. **No opportunities detected**
   - Check RPC connection quality
   - Verify mempool monitoring is working
   - Lower profit thresholds temporarily

2. **High gas cost estimates**
   - Normal on mainnet during high congestion
   - Adjust `MAX_GAS_PRICE_GWEI` settings
   - Consider gas optimization strategies

3. **Simulation vs real execution differences**
   - Simulation uses estimates, real execution varies
   - Network conditions change rapidly
   - Always test with small amounts first

## Safety Notes

⚠️ **Important Reminders**:
- Simulation mode is **read-only** - no transactions are sent
- **Real funds are never at risk** in simulation mode
- **Switch carefully** when moving to live execution
- **Test thoroughly** before deploying real capital

## Next Steps

1. **Run simulation mode** on mainnet to see live opportunities
2. **Analyze the data** to understand MEV landscape
3. **Optimize strategies** based on simulation results
4. **Deploy contracts** when ready for live execution
5. **Switch to live mode** with appropriate risk management
