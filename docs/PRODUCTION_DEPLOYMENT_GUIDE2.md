# 🚀 MAINNET PRODUCTION DEPLOYMENT GUIDE

## 📋 Pre-Deployment Checklist

### ✅ **COMPLETED:**
- [x] Environment variables configured for mainnet
- [x] Token addresses updated to mainnet
- [x] DEX factory addresses updated to mainnet  
- [x] Local mainnet node running and synced
- [x] DRY_RUN disabled for live trading
- [x] Private keys configured

### ❌ **REQUIRED ACTIONS:**

#### 1. **Fund Your Deployment Account**
```bash
# Your deployment address: ******************************************
# Required: At least 0.1 ETH for deployment + gas fees
# Recommended: 0.5 ETH for safety margin
```

**⚠️ CRITICAL**: Send ETH to `******************************************` before proceeding.

#### 2. **Deploy Hybrid Flashloan Contract**
```bash
# After funding the account, run:
npx hardhat run scripts/deploy-hybrid-flashloan.js --network local-mainnet
```

#### 3. **Update Contract Address**
```bash
# Copy the deployed contract address and update .env:
HYBRID_FLASHLOAN_CONTRACT=0x[CONTRACT_ADDRESS_FROM_DEPLOYMENT]
```

## 🔧 **Deployment Commands**

### **Option 1: Manual Deployment (Recommended)**
```bash
# 1. Fund account first (send ETH to ******************************************)

# 2. Deploy contract
npx hardhat run scripts/deploy-hybrid-flashloan.js --network local-mainnet

# 3. Update .env with contract address
# HYBRID_FLASHLOAN_CONTRACT=0x[DEPLOYED_ADDRESS]

# 4. Start MEV bot
npm run dev
```

### **Option 2: Automated Production Script**
```bash
# This script includes safety checks and confirmations
npm run deploy:mainnet-production
```

## 🛡️ **Safety Measures**

### **Start Conservative:**
```bash
# In .env, use conservative settings initially:
MAX_POSITION_SIZE_ETH=1          # Start small
MIN_PROFIT_WEI=***************** # 0.05 ETH minimum profit
DRY_RUN=false                    # Live trading
ENABLE_TRANSACTION_SIMULATION=true # Always simulate first
```

### **Monitor Closely:**
- Watch gas prices and network congestion
- Monitor failed transactions
- Track profit/loss carefully
- Have emergency stop procedures ready

## 📊 **Expected Deployment Costs**

| Component | Estimated Gas | Cost @ 30 gwei |
|-----------|---------------|----------------|
| Contract Deployment | ~2,500,000 gas | ~0.075 ETH |
| Contract Verification | ~50,000 gas | ~0.0015 ETH |
| Initial Test Transaction | ~400,000 gas | ~0.012 ETH |
| **Total** | **~3,000,000 gas** | **~0.09 ETH** |

**Recommended funding: 0.5 ETH** (includes safety margin)

## 🎯 **Post-Deployment Steps**

1. **Verify Contract on Etherscan**
2. **Test with Small Amounts**
3. **Monitor for 24 Hours**
4. **Gradually Increase Position Sizes**
5. **Set Up Alerts and Monitoring**

## 🚨 **Emergency Procedures**

### **Emergency Stop:**
```bash
# Set in .env and restart:
EMERGENCY_STOP=true
```

### **Withdraw Funds:**
```bash
# Use emergency withdrawal functions in contract
# Or transfer ownership to cold wallet
```

## 📞 **Support**

If deployment fails:
1. Check account balance
2. Verify node sync status  
3. Check gas prices
4. Review error messages
5. Try again with higher gas limits

---

**⚠️ REMEMBER: This is MAINNET with REAL MONEY. Start small and monitor closely!**
