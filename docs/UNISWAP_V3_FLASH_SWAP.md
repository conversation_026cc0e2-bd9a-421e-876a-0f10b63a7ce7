# 🔄 Uniswap V3 Flash Swap Arbitrage

This implementation focuses exclusively on **Uniswap V3 native flash swaps** for arbitrage between different fee tiers of the same token pair. No external flashloan providers (Aave/Balancer) are used.

## 🎯 **Strategy Overview**

### **Concept**
- Use Uniswap V3's built-in `flash()` function to borrow tokens temporarily
- Perform arbitrage between different fee tiers (0.05%, 0.3%, 1.0%) of the same pair
- Repay the flash loan + minimal fee in the same transaction
- Keep the profit from price differences between fee tiers

### **Example Flow**
```
1. Flash borrow 100,000 USDC from WETH/USDC 0.05% pool
2. Swap USDC → WETH on 0.05% pool (better price)
3. Swap WETH → USDC on 0.3% pool (worse price, favorable for arbitrage)
4. Repay flash loan + small fee to original pool
5. Keep surplus as profit
```

## 📊 **Supported Trading Pairs**

Only these specific pairs are monitored:

| Pair | Description | Fee Tiers Available |
|------|-------------|-------------------|
| **WETH/USDC** | ETH ↔ USD Coin | 0.05%, 0.3%, 1.0% |
| **WETH/USDT** | ETH ↔ Tether USD | 0.05%, 0.3%, 1.0% |
| **WBTC/WETH** | Bitcoin ↔ ETH | 0.05%, 0.3%, 1.0% |
| **DAI/USDC** | DAI ↔ USD Coin | 0.05%, 0.3%, 1.0% |

## ⚙️ **Configuration**

### **Environment Variables**

```bash
# Enable Uniswap V3 Flash Swaps
ENABLE_UNISWAP_V3_FLASH_SWAPS=true

# Disable other strategies
ENABLE_FLASHLOAN_ATTACKS=false
ENABLE_ARBITRAGE=false
ENABLE_SANDWICH_ATTACKS=false

# Specific trading pairs
UNISWAP_V3_TRADING_PAIRS=WETH/USDC,WETH/USDT,WBTC/WETH,DAI/USDC

# Fee tiers to monitor
UNISWAP_V3_FEE_TIERS=500,3000,10000  # 0.05%, 0.3%, 1.0%

# Flash swap amounts
FLASHLOAN_BASE_AMOUNT_WETH=10
FLASHLOAN_BASE_AMOUNT_USDC=30000
FLASHLOAN_BASE_AMOUNT_USDT=30000
FLASHLOAN_BASE_AMOUNT_WBTC=1
FLASHLOAN_BASE_AMOUNT_DAI=30000

# Minimum profit threshold
MIN_PROFIT_WEI=10000000000000000  # 0.01 ETH
```

## 🚀 **Deployment & Setup**

### **1. Test Configuration**
```bash
npm run test:uniswap-v3-config
```

### **2. Deploy Contract**
```bash
npm run deploy:uniswap-v3-flash
```

### **3. Start Bot**
```bash
npm run dev
```

## 🔧 **Technical Implementation**

### **Smart Contract: `UniswapV3FlashSwap.sol`**

Key functions:
- `executeFlashSwapArbitrage()` - Main arbitrage execution
- `uniswapV3FlashCallback()` - Flash swap callback
- `getPoolAddress()` - Get pool address for token pair + fee
- `checkArbitrageOpportunity()` - Check if arbitrage exists

### **Strategy Class: `UniswapV3FlashSwapStrategy.ts`**

Key methods:
- `scanForOpportunities()` - Scan all pairs and fee tiers
- `executeFlashSwap()` - Execute profitable opportunities
- `simulateFlashSwapArbitrage()` - Estimate profit before execution

## 💰 **Profit Calculation**

### **Revenue Sources**
1. **Price differences** between fee tiers
2. **Temporary inefficiencies** in pool pricing
3. **Large trades** causing price impact differences

### **Cost Factors**
1. **Flash swap fees** (minimal, usually 0.01-0.05%)
2. **Gas costs** (~300,000 gas per transaction)
3. **Slippage** on both swaps

### **Profitability Formula**
```
Profit = (Amount_Out_Final - Amount_In_Initial) - Flash_Fee - Gas_Cost
```

## 📈 **Advantages Over Traditional Flashloans**

| Aspect | Uniswap V3 Flash | Aave Flashloan | Balancer Flashloan |
|--------|------------------|----------------|-------------------|
| **Fees** | ~0.01-0.05% | 0.09% | 0% |
| **Capital Required** | None | None | None |
| **Complexity** | Medium | High | Medium |
| **Gas Cost** | ~300k | ~400k | ~350k |
| **Liquidity** | Pool-dependent | High | High |
| **Speed** | Fast | Fast | Fast |

## ⚠️ **Risk Factors**

### **Market Risks**
- **Price volatility** during execution
- **MEV competition** from other bots
- **Liquidity changes** between fee tiers

### **Technical Risks**
- **Gas price spikes** reducing profitability
- **Network congestion** causing failures
- **Smart contract bugs** or exploits

### **Operational Risks**
- **Insufficient pool liquidity** for large trades
- **Fee tier imbalances** reducing opportunities
- **Regulatory changes** affecting DeFi

## 🛡️ **Safety Features**

### **Built-in Protections**
- **Minimum profit thresholds** prevent unprofitable trades
- **Gas cost estimation** before execution
- **Slippage protection** on all swaps
- **Emergency withdrawal** function for stuck funds

### **Monitoring & Alerts**
- **Real-time profitability** tracking
- **Failed transaction** analysis
- **Gas cost optimization** suggestions
- **Opportunity detection** statistics

## 📊 **Performance Metrics**

### **Key Indicators**
- **Success rate** (% of profitable executions)
- **Average profit** per successful trade
- **Gas efficiency** (profit/gas ratio)
- **Opportunity frequency** (trades per hour)

### **Optimization Targets**
- **>80% success rate** for executed trades
- **>0.02 ETH average profit** per trade
- **<5% gas cost** relative to profit
- **>10 opportunities** per day

## 🔍 **Monitoring & Debugging**

### **Dashboard Metrics**
- Current trading pairs and fee tiers
- Detected opportunities (profitable vs unprofitable)
- Execution success/failure rates
- Real-time profit tracking

### **Log Analysis**
```bash
# View flash swap specific logs
grep "flash swap" logs/mev-bot.log

# Monitor opportunity detection
grep "opportunity detected" logs/mev-bot.log

# Check execution results
grep "flash swap executed" logs/mev-bot.log
```

## 🎯 **Optimization Tips**

### **Maximize Profitability**
1. **Monitor multiple fee tiers** simultaneously
2. **Adjust base amounts** based on market conditions
3. **Optimize gas prices** for network conditions
4. **Focus on high-volume pairs** for better opportunities

### **Reduce Risks**
1. **Start with small amounts** to test profitability
2. **Monitor gas costs** relative to expected profits
3. **Set conservative profit thresholds** initially
4. **Use simulation mode** for testing strategies

## 📚 **Additional Resources**

- [Uniswap V3 Flash Documentation](https://docs.uniswap.org/protocol/guides/flash-integrations/inheritance-constructors)
- [Fee Tier Analysis](https://info.uniswap.org/#/pools)
- [Gas Optimization Guide](./GAS_OPTIMIZATION.md)
- [Risk Management](./RISK_MANAGEMENT.md)

## 🆘 **Troubleshooting**

### **Common Issues**

**No opportunities detected:**
- Check pool liquidity for target pairs
- Verify fee tier availability
- Adjust minimum profit thresholds

**High gas costs:**
- Monitor network congestion
- Adjust gas price settings
- Consider smaller trade sizes

**Execution failures:**
- Check slippage tolerance settings
- Verify contract deployment
- Monitor for MEV competition

**Low profitability:**
- Increase base trade amounts
- Focus on volatile market periods
- Optimize fee tier combinations
