const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 MAINNET DEPLOYMENT READINESS CHECK");
  console.log("=====================================\n");

  try {
    // Get network info
    const network = await ethers.provider.getNetwork();
    console.log(`📡 Network: ${network.name} (Chain ID: ${network.chainId})`);
    
    if (network.chainId !== 1n) {
      console.log("❌ ERROR: Not connected to mainnet!");
      console.log("   Expected Chain ID: 1");
      console.log(`   Current Chain ID: ${network.chainId}`);
      process.exit(1);
    }
    
    console.log("✅ Connected to Ethereum Mainnet\n");

    // Get deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`👤 Deployer Address: ${deployer.address}`);
    
    // Check balance
    const balance = await ethers.provider.getBalance(deployer.address);
    const balanceEth = ethers.formatEther(balance);
    console.log(`💰 Current Balance: ${balanceEth} ETH`);
    
    // Check minimum requirements
    const minRequired = ethers.parseEther("0.1");
    const recommended = ethers.parseEther("0.5");
    
    console.log("\n📊 FUNDING REQUIREMENTS:");
    console.log(`   Minimum Required: 0.1 ETH`);
    console.log(`   Recommended: 0.5 ETH`);
    console.log(`   Current Balance: ${balanceEth} ETH`);
    
    if (balance < minRequired) {
      const needed = ethers.formatEther(minRequired - balance);
      console.log(`\n❌ INSUFFICIENT FUNDS`);
      console.log(`   Need at least: ${needed} ETH more`);
      console.log(`   Send ETH to: ${deployer.address}`);
      console.log(`\n🚨 CANNOT DEPLOY WITHOUT FUNDING`);
      return false;
    } else if (balance < recommended) {
      const suggested = ethers.formatEther(recommended - balance);
      console.log(`\n⚠️  MINIMAL FUNDING`);
      console.log(`   Suggested additional: ${suggested} ETH`);
      console.log(`   Current balance sufficient for deployment`);
      console.log(`   But recommended to add more for safety`);
    } else {
      console.log(`\n✅ WELL FUNDED`);
      console.log(`   Balance exceeds recommended amount`);
    }
    
    // Check gas price
    const feeData = await ethers.provider.getFeeData();
    const gasPriceGwei = ethers.formatUnits(feeData.gasPrice || 0n, "gwei");
    console.log(`\n⛽ Current Gas Price: ${gasPriceGwei} gwei`);
    
    // Estimate deployment cost
    const estimatedGas = 2500000n; // Estimated gas for contract deployment
    const deploymentCost = (feeData.gasPrice || 0n) * estimatedGas;
    const deploymentCostEth = ethers.formatEther(deploymentCost);
    
    console.log(`📊 Estimated Deployment Cost: ${deploymentCostEth} ETH`);
    
    // Check if ready to deploy
    if (balance >= minRequired) {
      console.log(`\n🚀 READY FOR DEPLOYMENT!`);
      console.log(`\nNext steps:`);
      console.log(`1. Run: npx hardhat run scripts/deploy-hybrid-flashloan.js --network local-mainnet`);
      console.log(`2. Update HYBRID_FLASHLOAN_CONTRACT in .env with deployed address`);
      console.log(`3. Start MEV bot: npm run dev`);
      return true;
    }
    
    return false;
    
  } catch (error) {
    console.error("❌ Error checking deployment readiness:", error.message);
    return false;
  }
}

main()
  .then((ready) => {
    if (ready) {
      console.log("\n✅ All checks passed - ready for deployment!");
      process.exit(0);
    } else {
      console.log("\n❌ Not ready for deployment - please address issues above");
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
