const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Deploying Minimal Test Contract...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  
  // Check balance
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");
  
  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId.toString());
  
  try {
    // Deploy the contract
    console.log("\n📦 Deploying MinimalTest contract...");
    
    const MinimalTest = await ethers.getContractFactory("MinimalTest");
    
    // Get current gas price and use a higher one
    const gasPrice = await deployer.provider.getFeeData();
    const higherGasPrice = (gasPrice.gasPrice || 1000000000n) * 3n; // 3x current gas price
    
    console.log("Current gas price:", ethers.formatUnits(gasPrice.gasPrice || 0n, "gwei"), "gwei");
    console.log("Using gas price:", ethers.formatUnits(higherGasPrice, "gwei"), "gwei");
    
    // Deploy with higher gas price and timeout
    const contract = await MinimalTest.deploy({
      gasLimit: 200000, // Small gas limit for minimal contract
      gasPrice: higherGasPrice
    });
    
    console.log("⏳ Waiting for deployment transaction (30s timeout)...");
    
    // Wait for deployment with timeout
    const deploymentPromise = contract.waitForDeployment();
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error("Deployment timeout")), 30000)
    );
    
    await Promise.race([deploymentPromise, timeoutPromise]);
    
    const contractAddress = await contract.getAddress();
    console.log("✅ MinimalTest deployed to:", contractAddress);
    
    // Get deployment transaction details
    const deployTx = contract.deploymentTransaction();
    if (deployTx) {
      const receipt = await deployTx.wait();
      console.log("Gas used:", receipt.gasUsed.toString());
      console.log("Gas price:", ethers.formatUnits(receipt.gasPrice, "gwei"), "gwei");
      console.log("Deployment cost:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "ETH");
      console.log("Transaction hash:", receipt.hash);
    }
    
    // Test basic contract functions
    console.log("\n🧪 Testing contract functions...");
    
    try {
      // Test basic function
      const message = await contract.getMessage();
      console.log("Contract message:", message);
      
      // Test deployment info
      const deploymentInfo = await contract.getDeploymentInfo();
      console.log("Contract owner:", deploymentInfo[0]);
      console.log("Deployment time:", new Date(Number(deploymentInfo[1]) * 1000).toISOString());
      console.log("Current message:", deploymentInfo[2]);
      
      console.log("✅ All contract functions working correctly!");
      
    } catch (error) {
      console.warn("⚠️  Contract function test failed:", error.message);
    }
    
    // Display summary
    console.log("\n🎉 MINIMAL TEST DEPLOYMENT SUCCESSFUL!");
    console.log("==========================================");
    console.log("Contract Address:", contractAddress);
    console.log("Network:", network.name);
    console.log("Chain ID:", network.chainId.toString());
    console.log("Deployer:", deployer.address);
    console.log("==========================================");
    
    console.log("\n✅ Basic deployment is working!");
    console.log("The issue with the Uniswap V3 contract might be:");
    console.log("1. Complex contract size causing deployment issues");
    console.log("2. External contract calls in constructor");
    console.log("3. Gas estimation problems");
    
    return contractAddress;
    
  } catch (error) {
    console.error("❌ Minimal test deployment failed:", error.message);
    
    if (error.message.includes("timeout")) {
      console.error("💡 The deployment is taking too long. This might be due to:");
      console.error("   - Network congestion");
      console.error("   - Low gas price");
      console.error("   - RPC node issues");
    } else if (error.message.includes("insufficient funds")) {
      console.error("💡 Solution: Add more ETH to your deployer account");
    } else if (error.message.includes("gas")) {
      console.error("💡 Solution: Try increasing gas limit or gas price");
    }
    
    console.error("\nFull error:", error);
    process.exit(1);
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Minimal test deployment script failed:", error);
    process.exit(1);
  });
