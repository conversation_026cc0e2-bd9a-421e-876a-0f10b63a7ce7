const { ethers } = require("hardhat");
const axios = require("axios");

// Cost protection configuration
const MAX_DEPLOYMENT_COST_USD = 30; // Maximum $30 deployment cost
const ETH_PRICE_API = "https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd";
const ESTIMATED_GAS_LIMIT = 2500000n; // Conservative gas estimate for deployment
const GAS_BUFFER = 100000n; // Additional gas buffer

async function getETHPriceUSD() {
  try {
    const response = await axios.get(ETH_PRICE_API, { timeout: 5000 });
    return response.data.ethereum.usd;
  } catch (error) {
    console.log("⚠️  Could not fetch ETH price, using fallback: $2000");
    return 2000; // Fallback price
  }
}

async function getOptimizedGasPrice() {
  try {
    // Get current network gas prices
    const feeData = await ethers.provider.getFeeData();
    
    // Try multiple gas estimation sources
    const gasEstimates = await Promise.allSettled([
      getEthGasStationPrice(),
      getGasNowPrice(),
      getProviderGasPrice(feeData)
    ]);

    const validEstimates = gasEstimates
      .filter(result => result.status === 'fulfilled' && result.value)
      .map(result => result.value);

    if (validEstimates.length === 0) {
      throw new Error("All gas estimation sources failed");
    }

    // Use the median gas price for optimal cost
    validEstimates.sort((a, b) => Number(a.gasPrice - b.gasPrice));
    const medianIndex = Math.floor(validEstimates.length / 2);
    const optimalGas = validEstimates[medianIndex];

    console.log(`⛽ Gas Price Sources (${validEstimates.length} available):`);
    validEstimates.forEach(estimate => {
      const gwei = ethers.formatUnits(estimate.gasPrice, 'gwei');
      console.log(`   ${estimate.source}: ${gwei} gwei`);
    });

    return optimalGas;

  } catch (error) {
    console.log("⚠️  Using provider gas price as fallback");
    const feeData = await ethers.provider.getFeeData();
    return getProviderGasPrice(feeData);
  }
}

async function getEthGasStationPrice() {
  try {
    const response = await axios.get('https://ethgasstation.info/api/ethgasAPI.json', { timeout: 3000 });
    const gasPrice = ethers.parseUnits((response.data.standard / 10).toString(), 'gwei');
    return { gasPrice, source: 'ETH Gas Station' };
  } catch (error) {
    return null;
  }
}

async function getGasNowPrice() {
  try {
    const response = await axios.get('https://www.gasnow.org/api/v3/gas/price', { timeout: 3000 });
    const gasPrice = ethers.parseUnits(response.data.data.standard.toString(), 'wei');
    return { gasPrice, source: 'GasNow' };
  } catch (error) {
    return null;
  }
}

async function getProviderGasPrice(feeData) {
  const gasPrice = feeData.gasPrice || feeData.maxFeePerGas || ethers.parseUnits('20', 'gwei');
  return { 
    gasPrice, 
    maxFeePerGas: feeData.maxFeePerGas,
    maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
    source: 'Provider' 
  };
}

async function calculateDeploymentCost(gasPrice, ethPriceUSD) {
  const totalGas = ESTIMATED_GAS_LIMIT + GAS_BUFFER;
  const costWei = gasPrice * totalGas;
  const costETH = Number(ethers.formatEther(costWei));
  const costUSD = costETH * ethPriceUSD;
  
  return {
    totalGas,
    costWei,
    costETH,
    costUSD,
    gasPrice
  };
}

async function waitForBetterGasPrice(maxCostUSD, ethPriceUSD, retries = 5) {
  console.log(`\n⏳ Waiting for better gas prices (max ${retries} attempts)...`);
  
  for (let i = 0; i < retries; i++) {
    console.log(`\n🔄 Attempt ${i + 1}/${retries}:`);
    
    const gasData = await getOptimizedGasPrice();
    const cost = await calculateDeploymentCost(gasData.gasPrice, ethPriceUSD);
    
    console.log(`   Current cost: $${cost.costUSD.toFixed(2)} (${cost.costETH.toFixed(4)} ETH)`);
    
    if (cost.costUSD <= maxCostUSD) {
      console.log(`✅ Acceptable gas price found!`);
      return { gasData, cost };
    }
    
    if (i < retries - 1) {
      console.log(`   Too expensive, waiting 30 seconds...`);
      await new Promise(resolve => setTimeout(resolve, 30000));
    }
  }
  
  return null;
}

async function main() {
  console.log("💰 COST-OPTIMIZED HYBRID FLASHLOAN DEPLOYMENT");
  console.log("=".repeat(50));
  console.log(`💵 Maximum deployment cost: $${MAX_DEPLOYMENT_COST_USD}`);
  console.log("");

  // Get network info
  const network = await ethers.provider.getNetwork();
  const chainId = Number(network.chainId);
  
  if (chainId !== 1) {
    console.log("⚠️  Cost optimization is designed for mainnet");
    console.log("   Proceeding with standard deployment...");
  }

  // Network-specific addresses
  let AAVE_POOL_ADDRESSES_PROVIDER, BALANCER_VAULT, networkName;
  
  if (chainId === 1) {
    AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
    BALANCER_VAULT = "******************************************";
    networkName = "Mainnet";
  } else if (chainId === 11155111) {
    AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
    BALANCER_VAULT = "******************************************";
    networkName = "Sepolia";
  } else {
    console.error(`❌ Unsupported network: Chain ID ${chainId}`);
    process.exit(1);
  }

  console.log(`📋 Deployment Details:`);
  console.log(`Network: ${networkName} (Chain ID: ${chainId})`);
  console.log(`Aave Pool Provider: ${AAVE_POOL_ADDRESSES_PROVIDER}`);
  console.log(`Balancer Vault: ${BALANCER_VAULT}`);

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  const deployerBalance = await ethers.provider.getBalance(deployer.address);
  
  console.log(`\n👤 Deployer: ${deployer.address}`);
  console.log(`💰 Balance: ${ethers.formatEther(deployerBalance)} ETH`);

  // Check minimum balance
  const minBalance = ethers.parseEther("0.05");
  if (deployerBalance < minBalance) {
    console.error(`❌ Insufficient balance. Need at least ${ethers.formatEther(minBalance)} ETH.`);
    process.exit(1);
  }

  // Get ETH price for cost calculation
  console.log("\n💱 Fetching ETH price...");
  const ethPriceUSD = await getETHPriceUSD();
  console.log(`📈 ETH Price: $${ethPriceUSD.toFixed(2)}`);

  // Get optimal gas price
  console.log("\n⛽ Analyzing gas prices...");
  const gasData = await getOptimizedGasPrice();
  const initialCost = await calculateDeploymentCost(gasData.gasPrice, ethPriceUSD);

  console.log(`\n💰 DEPLOYMENT COST ANALYSIS:`);
  console.log(`   Gas Limit: ${initialCost.totalGas.toLocaleString()}`);
  console.log(`   Gas Price: ${ethers.formatUnits(initialCost.gasPrice, 'gwei')} gwei`);
  console.log(`   Cost in ETH: ${initialCost.costETH.toFixed(4)} ETH`);
  console.log(`   Cost in USD: $${initialCost.costUSD.toFixed(2)}`);

  // Check if cost is acceptable
  if (initialCost.costUSD > MAX_DEPLOYMENT_COST_USD) {
    console.log(`\n❌ DEPLOYMENT COST TOO HIGH!`);
    console.log(`   Current cost: $${initialCost.costUSD.toFixed(2)}`);
    console.log(`   Maximum allowed: $${MAX_DEPLOYMENT_COST_USD}`);
    console.log(`   Difference: $${(initialCost.costUSD - MAX_DEPLOYMENT_COST_USD).toFixed(2)} over budget`);
    
    // Try to wait for better gas prices
    const betterGas = await waitForBetterGasPrice(MAX_DEPLOYMENT_COST_USD, ethPriceUSD);
    
    if (!betterGas) {
      console.log(`\n🚫 DEPLOYMENT CANCELLED`);
      console.log(`   Could not find acceptable gas prices within budget`);
      console.log(`   Current network congestion is too high`);
      console.log(`\n💡 Suggestions:`);
      console.log(`   1. Try again during off-peak hours (weekends, late night UTC)`);
      console.log(`   2. Increase budget above $${MAX_DEPLOYMENT_COST_USD}`);
      console.log(`   3. Wait for lower network congestion`);
      process.exit(1);
    }
    
    // Use the better gas price found
    Object.assign(gasData, betterGas.gasData);
    Object.assign(initialCost, betterGas.cost);
  }

  console.log(`\n✅ COST APPROVED - PROCEEDING WITH DEPLOYMENT`);
  console.log(`   Final cost: $${initialCost.costUSD.toFixed(2)} (within $${MAX_DEPLOYMENT_COST_USD} budget)`);

  // Get contract factory
  const HybridFlashloanArbitrage = await ethers.getContractFactory("HybridFlashloanArbitrage");

  // Prepare deployment transaction with optimized gas
  const deployTx = {
    gasLimit: initialCost.totalGas,
    gasPrice: gasData.gasPrice
  };

  // Use EIP-1559 if available
  if (gasData.maxFeePerGas && gasData.maxPriorityFeePerGas) {
    delete deployTx.gasPrice;
    deployTx.maxFeePerGas = gasData.maxFeePerGas;
    deployTx.maxPriorityFeePerGas = gasData.maxPriorityFeePerGas;
    console.log(`   Using EIP-1559: ${ethers.formatUnits(gasData.maxFeePerGas, 'gwei')} gwei max fee`);
  } else {
    console.log(`   Using legacy gas: ${ethers.formatUnits(gasData.gasPrice, 'gwei')} gwei`);
  }

  // Deploy the contract
  console.log("\n📝 Deploying contract with cost optimization...");
  const hybridFlashloan = await HybridFlashloanArbitrage.deploy(
    AAVE_POOL_ADDRESSES_PROVIDER,
    BALANCER_VAULT,
    deployTx
  );

  console.log("⏳ Waiting for deployment confirmation...");
  const receipt = await hybridFlashloan.waitForDeployment();
  
  const contractAddress = await hybridFlashloan.getAddress();
  console.log("✅ HybridFlashloanArbitrage deployed successfully!");

  // Calculate actual cost
  const deploymentReceipt = await ethers.provider.getTransactionReceipt(hybridFlashloan.deploymentTransaction().hash);
  const actualGasUsed = deploymentReceipt.gasUsed;
  const actualCostWei = actualGasUsed * (deploymentReceipt.gasPrice || gasData.gasPrice);
  const actualCostETH = Number(ethers.formatEther(actualCostWei));
  const actualCostUSD = actualCostETH * ethPriceUSD;

  console.log(`\n💰 ACTUAL DEPLOYMENT COST:`);
  console.log(`   Gas Used: ${actualGasUsed.toLocaleString()} (${((Number(actualGasUsed) / Number(initialCost.totalGas)) * 100).toFixed(1)}% of estimate)`);
  console.log(`   Actual Cost: ${actualCostETH.toFixed(4)} ETH ($${actualCostUSD.toFixed(2)})`);
  console.log(`   Savings: $${(initialCost.costUSD - actualCostUSD).toFixed(2)}`);

  // Verify deployment
  console.log("\n🔍 Verifying deployment...");
  const owner = await hybridFlashloan.owner();
  const chainIdContract = await hybridFlashloan.CHAIN_ID();

  console.log("\n📊 Contract Verification:");
  console.log("=".repeat(50));
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Owner: ${owner}`);
  console.log(`Chain ID: ${chainIdContract}`);
  console.log(`Deployment Cost: $${actualCostUSD.toFixed(2)} (${actualCostUSD <= MAX_DEPLOYMENT_COST_USD ? '✅ Within Budget' : '❌ Over Budget'})`);
  console.log("=".repeat(50));

  // Save deployment info
  const deploymentInfo = {
    network: networkName.toLowerCase(),
    chainId: chainId,
    contractAddress: contractAddress,
    owner: owner,
    deploymentCost: {
      gasUsed: actualGasUsed.toString(),
      costETH: actualCostETH,
      costUSD: actualCostUSD,
      gasPrice: gasData.gasPrice.toString(),
      ethPrice: ethPriceUSD
    },
    deployedAt: new Date().toISOString()
  };

  const fs = require('fs');
  const filename = `deployment-hybrid-${networkName.toLowerCase()}-optimized.json`;
  fs.writeFileSync(filename, JSON.stringify(deploymentInfo, null, 2));

  console.log("\n🎉 COST-OPTIMIZED DEPLOYMENT COMPLETED!");
  console.log("=".repeat(50));
  console.log("🔧 NEXT STEPS:");
  console.log("1. Update your .env file:");
  console.log(`   HYBRID_FLASHLOAN_CONTRACT=${contractAddress}`);
  console.log("2. Start MEV bot: npm run dev");
  console.log("");
  console.log(`📄 Deployment details saved to: ${filename}`);
  console.log("=".repeat(50));

  return contractAddress;
}

// Handle errors
main()
  .then((contractAddress) => {
    console.log(`\n🎉 Cost-optimized deployment completed!`);
    console.log(`Contract Address: ${contractAddress}`);
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
