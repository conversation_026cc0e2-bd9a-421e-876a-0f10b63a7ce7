const { ethers } = require("ethers");
require('dotenv').config();

// Mainnet addresses
const ADDRESSES = {
  WETH: "******************************************",
  USDC: "******************************************",
  USDT: "******************************************",
  DAI: "******************************************",
  WBTC: "******************************************",
  UNISWAP_V3_FACTORY: "******************************************"
};

const FEE_TIERS = [500, 3000, 10000]; // 0.05%, 0.3%, 1.0%

const TRADING_PAIRS = [
  { tokenA: 'WETH', tokenB: 'USDC' },
  { tokenA: 'WETH', tokenB: 'USDT' },
  { tokenA: 'WBTC', tokenB: 'WETH' },
  { tokenA: 'DAI', tokenB: 'USDC' }
];

async function main() {
  console.log("🧪 Testing Uniswap V3 Flash Swap Configuration");
  console.log("==============================================");
  
  // Check environment variables
  console.log("\n📋 Environment Configuration:");
  console.log("RPC_URL:", process.env.RPC_URL ? "✅ Set" : "❌ Missing");
  console.log("CHAIN_ID:", process.env.CHAIN_ID);
  console.log("ENABLE_UNISWAP_V3_FLASH_SWAPS:", process.env.ENABLE_UNISWAP_V3_FLASH_SWAPS);
  console.log("UNISWAP_V3_TRADING_PAIRS:", process.env.UNISWAP_V3_TRADING_PAIRS);
  console.log("UNISWAP_V3_FEE_TIERS:", process.env.UNISWAP_V3_FEE_TIERS);
  
  if (process.env.CHAIN_ID !== "1") {
    console.error("❌ Chain ID must be 1 (mainnet) for Uniswap V3 flash swaps");
    process.exit(1);
  }
  
  if (!process.env.RPC_URL) {
    console.error("❌ RPC_URL not configured");
    process.exit(1);
  }
  
  // Initialize provider
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
  
  try {
    // Test RPC connection
    console.log("\n🔗 Testing RPC Connection:");
    const blockNumber = await provider.getBlockNumber();
    const network = await provider.getNetwork();
    
    console.log("✅ Connected to network:", network.name);
    console.log("✅ Chain ID:", network.chainId.toString());
    console.log("✅ Latest block:", blockNumber);
    
    if (network.chainId !== 1n) {
      console.error("❌ Not connected to Ethereum mainnet");
      process.exit(1);
    }
    
  } catch (error) {
    console.error("❌ RPC connection failed:", error.message);
    process.exit(1);
  }
  
  // Test Uniswap V3 Factory
  console.log("\n🏭 Testing Uniswap V3 Factory:");
  
  const factoryABI = [
    "function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool)"
  ];
  
  const factory = new ethers.Contract(ADDRESSES.UNISWAP_V3_FACTORY, factoryABI, provider);
  
  // Test each trading pair and fee tier combination
  console.log("\n💱 Testing Trading Pairs and Fee Tiers:");
  
  const poolResults = [];
  
  for (const pair of TRADING_PAIRS) {
    console.log(`\n📊 ${pair.tokenA}/${pair.tokenB}:`);
    
    const tokenAAddress = ADDRESSES[pair.tokenA];
    const tokenBAddress = ADDRESSES[pair.tokenB];
    
    for (const fee of FEE_TIERS) {
      try {
        const poolAddress = await factory.getPool(tokenAAddress, tokenBAddress, fee);
        
        if (poolAddress === ethers.ZeroAddress) {
          console.log(`   ${fee/10000}%: ❌ Pool not found`);
        } else {
          console.log(`   ${fee/10000}%: ✅ ${poolAddress}`);
          
          // Test pool liquidity
          const poolABI = [
            "function liquidity() external view returns (uint128)",
            "function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)"
          ];
          
          try {
            const pool = new ethers.Contract(poolAddress, poolABI, provider);
            const liquidity = await pool.liquidity();
            const slot0 = await pool.slot0();
            
            console.log(`      Liquidity: ${liquidity.toString()}`);
            console.log(`      Price: ${slot0.sqrtPriceX96.toString()}`);
            console.log(`      Unlocked: ${slot0.unlocked ? "✅" : "❌"}`);
            
            poolResults.push({
              pair: `${pair.tokenA}/${pair.tokenB}`,
              fee: fee,
              address: poolAddress,
              liquidity: liquidity.toString(),
              unlocked: slot0.unlocked
            });
            
          } catch (poolError) {
            console.log(`      ⚠️  Pool data error: ${poolError.message}`);
          }
        }
      } catch (error) {
        console.log(`   ${fee/10000}%: ❌ Error: ${error.message}`);
      }
    }
  }
  
  // Analyze arbitrage opportunities
  console.log("\n🔍 Analyzing Potential Arbitrage Opportunities:");
  
  const pairGroups = {};
  poolResults.forEach(pool => {
    if (!pairGroups[pool.pair]) {
      pairGroups[pool.pair] = [];
    }
    pairGroups[pool.pair].push(pool);
  });
  
  for (const [pairName, pools] of Object.entries(pairGroups)) {
    if (pools.length >= 2) {
      console.log(`\n💰 ${pairName}:`);
      console.log(`   Available fee tiers: ${pools.map(p => `${p.fee/10000}%`).join(', ')}`);
      console.log(`   Potential arbitrage combinations: ${pools.length * (pools.length - 1)} pairs`);
      
      // Check for good arbitrage combinations
      const lowFeePools = pools.filter(p => p.fee <= 3000);
      const highFeePools = pools.filter(p => p.fee >= 3000);
      
      if (lowFeePools.length > 0 && highFeePools.length > 0) {
        console.log(`   ✅ Good arbitrage potential (low fee → high fee)`);
      } else {
        console.log(`   ⚠️  Limited arbitrage potential`);
      }
    } else {
      console.log(`\n⚠️  ${pairName}: Only ${pools.length} pool(s) available - limited arbitrage potential`);
    }
  }
  
  // Test contract deployment readiness
  console.log("\n🚀 Contract Deployment Readiness:");
  
  if (process.env.PRIVATE_KEY) {
    try {
      const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
      const balance = await provider.getBalance(wallet.address);
      
      console.log("Deployer address:", wallet.address);
      console.log("Balance:", ethers.formatEther(balance), "ETH");
      
      if (balance >= ethers.parseEther("0.1")) {
        console.log("✅ Sufficient balance for deployment");
      } else {
        console.log("❌ Insufficient balance for deployment (need at least 0.1 ETH)");
      }
    } catch (error) {
      console.log("❌ Invalid private key");
    }
  } else {
    console.log("❌ PRIVATE_KEY not configured");
  }
  
  // Summary
  console.log("\n📊 SUMMARY:");
  console.log("=============");
  console.log(`Total pools found: ${poolResults.length}`);
  console.log(`Trading pairs with multiple fee tiers: ${Object.values(pairGroups).filter(pools => pools.length >= 2).length}`);
  console.log(`Potential arbitrage combinations: ${Object.values(pairGroups).reduce((sum, pools) => sum + (pools.length >= 2 ? pools.length * (pools.length - 1) : 0), 0)}`);
  
  const readyForDeployment = 
    process.env.CHAIN_ID === "1" &&
    process.env.RPC_URL &&
    process.env.PRIVATE_KEY &&
    poolResults.length > 0;
  
  if (readyForDeployment) {
    console.log("\n✅ READY FOR DEPLOYMENT!");
    console.log("Run: npm run deploy:uniswap-v3-flash");
  } else {
    console.log("\n❌ NOT READY FOR DEPLOYMENT");
    console.log("Please fix the issues above before deploying");
  }
  
  console.log("\n🎯 RECOMMENDED NEXT STEPS:");
  console.log("1. Deploy the UniswapV3FlashSwap contract");
  console.log("2. Start the MEV bot with ENABLE_UNISWAP_V3_FLASH_SWAPS=true");
  console.log("3. Monitor for arbitrage opportunities between fee tiers");
  console.log("4. Start with small amounts to test profitability");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
