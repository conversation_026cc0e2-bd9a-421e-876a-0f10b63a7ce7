const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Deploying Test Uniswap V3 Contract...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  
  // Check balance
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");
  
  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId.toString());
  
  try {
    // Deploy the contract
    console.log("\n📦 Deploying TestUniswapV3 contract...");
    
    const TestUniswapV3 = await ethers.getContractFactory("TestUniswapV3");
    
    // Get current gas price
    const gasPrice = await deployer.provider.getFeeData();
    console.log("Current gas price:", ethers.formatUnits(gasPrice.gasPrice || 0n, "gwei"), "gwei");
    
    // Deploy with higher gas price for faster confirmation
    const higherGasPrice = (gasPrice.gasPrice || 1000000000n) * 2n; // 2x current gas price
    console.log("Using gas price:", ethers.formatUnits(higherGasPrice, "gwei"), "gwei");

    const contract = await TestUniswapV3.deploy({
      gasLimit: 300000, // Conservative gas limit
      gasPrice: higherGasPrice
    });
    
    console.log("⏳ Waiting for deployment transaction...");
    await contract.waitForDeployment();
    
    const contractAddress = await contract.getAddress();
    console.log("✅ TestUniswapV3 deployed to:", contractAddress);
    
    // Get deployment transaction details
    const deployTx = contract.deploymentTransaction();
    if (deployTx) {
      const receipt = await deployTx.wait();
      console.log("Gas used:", receipt.gasUsed.toString());
      console.log("Gas price:", ethers.formatUnits(receipt.gasPrice, "gwei"), "gwei");
      console.log("Deployment cost:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "ETH");
    }
    
    // Test basic contract functions
    console.log("\n🧪 Testing contract functions...");
    
    try {
      // Test basic function
      const testResult = await contract.testFunction();
      console.log("Test function result:", testResult);
      
      // Test contract info
      const contractInfo = await contract.getContractInfo();
      console.log("Contract owner:", contractInfo[0]);
      console.log("Factory address:", contractInfo[1]);
      console.log("WETH address:", contractInfo[2]);
      console.log("USDC address:", contractInfo[3]);
      
      // Test pool address lookup
      const wethAddress = "******************************************";
      const usdcAddress = "******************************************";
      
      const poolAddress = await contract.getPoolAddress(wethAddress, usdcAddress, 3000);
      console.log("WETH/USDC 0.3% pool address:", poolAddress);
      
      if (poolAddress === ethers.ZeroAddress) {
        console.warn("⚠️  WETH/USDC pool not found");
      } else {
        console.log("✅ Pool address lookup working");
      }
      
      // Test getting all WETH/USDC pools
      console.log("\n🔍 Getting all WETH/USDC pool addresses...");
      const pools = await contract.getWETHUSDCPools();
      console.log("0.05% pool:", pools[0]);
      console.log("0.3% pool:", pools[1]);
      console.log("1.0% pool:", pools[2]);
      
    } catch (error) {
      console.warn("⚠️  Contract function test failed:", error.message);
    }
    
    // Display summary
    console.log("\n🎉 TEST DEPLOYMENT SUCCESSFUL!");
    console.log("==========================================");
    console.log("Contract Address:", contractAddress);
    console.log("Network:", network.name);
    console.log("Chain ID:", network.chainId.toString());
    console.log("Deployer:", deployer.address);
    console.log("==========================================");
    
    console.log("\n✅ The basic Uniswap V3 integration is working!");
    console.log("You can now proceed with the full flash swap contract deployment.");
    
  } catch (error) {
    console.error("❌ Test deployment failed:", error.message);
    console.error("\nFull error:", error);
    
    if (error.message.includes("insufficient funds")) {
      console.error("💡 Solution: Add more ETH to your deployer account");
    } else if (error.message.includes("gas")) {
      console.error("💡 Solution: Try increasing gas limit or gas price");
    } else if (error.message.includes("nonce")) {
      console.error("💡 Solution: Wait a moment and try again");
    }
    
    process.exit(1);
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test deployment script failed:", error);
    process.exit(1);
  });
