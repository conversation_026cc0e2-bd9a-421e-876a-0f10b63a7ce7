const { ethers } = require("hardhat");
const axios = require("axios");

// Configuration
const MAX_DEPLOYMENT_COST_USD = 30;
const CHECK_INTERVAL_MS = 30000; // Check every 30 seconds
const ETH_PRICE_API = "https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd";
const ESTIMATED_GAS_LIMIT = 2500000n;

async function getETHPriceUSD() {
  try {
    const response = await axios.get(ETH_PRICE_API, { timeout: 5000 });
    return response.data.ethereum.usd;
  } catch (error) {
    return 2000; // Fallback price
  }
}

async function getGasPrices() {
  try {
    const feeData = await ethers.provider.getFeeData();
    
    // Get multiple sources
    const sources = await Promise.allSettled([
      getEthGasStationPrice(),
      getGasNowPrice(),
      getProviderGasPrice(feeData)
    ]);

    const validSources = sources
      .filter(result => result.status === 'fulfilled' && result.value)
      .map(result => result.value);

    return validSources.length > 0 ? validSources : [getProviderGasPrice(feeData)];
  } catch (error) {
    const feeData = await ethers.provider.getFeeData();
    return [getProviderGasPrice(feeData)];
  }
}

async function getEthGasStationPrice() {
  try {
    const response = await axios.get('https://ethgasstation.info/api/ethgasAPI.json', { timeout: 3000 });
    return {
      gasPrice: ethers.parseUnits((response.data.standard / 10).toString(), 'gwei'),
      source: 'ETH Gas Station',
      level: 'standard'
    };
  } catch (error) {
    return null;
  }
}

async function getGasNowPrice() {
  try {
    const response = await axios.get('https://www.gasnow.org/api/v3/gas/price', { timeout: 3000 });
    return {
      gasPrice: ethers.parseUnits(response.data.data.standard.toString(), 'wei'),
      source: 'GasNow',
      level: 'standard'
    };
  } catch (error) {
    return null;
  }
}

function getProviderGasPrice(feeData) {
  return {
    gasPrice: feeData.gasPrice || feeData.maxFeePerGas || ethers.parseUnits('20', 'gwei'),
    source: 'Provider',
    level: 'current'
  };
}

function calculateCost(gasPrice, ethPriceUSD) {
  const costWei = gasPrice * ESTIMATED_GAS_LIMIT;
  const costETH = Number(ethers.formatEther(costWei));
  const costUSD = costETH * ethPriceUSD;
  
  return { costETH, costUSD };
}

function getColorCode(costUSD, maxCostUSD) {
  if (costUSD <= maxCostUSD * 0.7) return '\x1b[32m'; // Green - Great price
  if (costUSD <= maxCostUSD * 0.85) return '\x1b[33m'; // Yellow - Good price
  if (costUSD <= maxCostUSD) return '\x1b[36m'; // Cyan - Acceptable
  return '\x1b[31m'; // Red - Too expensive
}

function formatTime() {
  return new Date().toLocaleTimeString();
}

async function monitorGasPrices() {
  console.log("⛽ GAS PRICE MONITOR FOR DEPLOYMENT");
  console.log("=".repeat(60));
  console.log(`💵 Target: Deploy when cost ≤ $${MAX_DEPLOYMENT_COST_USD}`);
  console.log(`🔄 Checking every ${CHECK_INTERVAL_MS / 1000} seconds`);
  console.log(`⏹️  Press Ctrl+C to stop monitoring`);
  console.log("");

  let ethPrice = await getETHPriceUSD();
  let bestPrice = Infinity;
  let bestTime = null;
  let checkCount = 0;

  // Update ETH price every 5 minutes
  setInterval(async () => {
    ethPrice = await getETHPriceUSD();
  }, 300000);

  while (true) {
    try {
      checkCount++;
      const gasPrices = await getGasPrices();
      
      // Find the best (lowest) gas price
      const bestGas = gasPrices.reduce((best, current) => 
        current.gasPrice < best.gasPrice ? current : best
      );

      const cost = calculateCost(bestGas.gasPrice, ethPrice);
      const colorCode = getColorCode(cost.costUSD, MAX_DEPLOYMENT_COST_USD);
      const resetColor = '\x1b[0m';

      // Track best price seen
      if (cost.costUSD < bestPrice) {
        bestPrice = cost.costUSD;
        bestTime = formatTime();
      }

      // Clear line and print status
      process.stdout.write('\r\x1b[K'); // Clear current line
      
      const status = cost.costUSD <= MAX_DEPLOYMENT_COST_USD ? '✅ DEPLOY NOW' : '⏳ WAIT';
      const gweiPrice = ethers.formatUnits(bestGas.gasPrice, 'gwei');
      
      process.stdout.write(
        `${colorCode}[${formatTime()}] ${status} | ` +
        `${gweiPrice} gwei | $${cost.costUSD.toFixed(2)} | ` +
        `Best: $${bestPrice.toFixed(2)} at ${bestTime || 'N/A'}${resetColor}`
      );

      // Show detailed info every 10 checks
      if (checkCount % 10 === 0) {
        console.log('\n');
        console.log(`📊 Detailed Gas Analysis (Check #${checkCount}):`);
        console.log(`   ETH Price: $${ethPrice.toFixed(2)}`);
        
        gasPrices.forEach(gas => {
          const cost = calculateCost(gas.gasPrice, ethPrice);
          const gwei = ethers.formatUnits(gas.gasPrice, 'gwei');
          const color = getColorCode(cost.costUSD, MAX_DEPLOYMENT_COST_USD);
          console.log(`   ${color}${gas.source}: ${gwei} gwei ($${cost.costUSD.toFixed(2)})${resetColor}`);
        });
        
        console.log(`   Best price today: $${bestPrice.toFixed(2)} at ${bestTime || 'N/A'}`);
        console.log('');
      }

      // Alert if price is good
      if (cost.costUSD <= MAX_DEPLOYMENT_COST_USD && checkCount > 1) {
        console.log('\n');
        console.log('🚨 DEPLOYMENT OPPORTUNITY DETECTED! 🚨');
        console.log(`💰 Current cost: $${cost.costUSD.toFixed(2)} (within budget)`);
        console.log(`⛽ Gas price: ${gweiPrice} gwei`);
        console.log('');
        console.log('🚀 Run deployment command:');
        console.log('   npm run deploy:cost-optimized');
        console.log('');
        
        // Beep sound (if terminal supports it)
        process.stdout.write('\x07');
      }

      await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL_MS));

    } catch (error) {
      console.log(`\n❌ Error monitoring gas prices: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL_MS));
    }
  }
}

async function main() {
  // Check if connected to mainnet
  const network = await ethers.provider.getNetwork();
  const chainId = Number(network.chainId);
  
  if (chainId !== 1) {
    console.log(`⚠️  Connected to ${network.name} (Chain ID: ${chainId})`);
    console.log('   Gas monitoring is optimized for mainnet');
    console.log('   Continuing anyway...\n');
  }

  await monitorGasPrices();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n👋 Gas price monitoring stopped');
  console.log('💡 Run "npm run deploy:cost-optimized" when ready to deploy');
  process.exit(0);
});

main().catch((error) => {
  console.error('❌ Gas monitoring failed:', error);
  process.exit(1);
});
