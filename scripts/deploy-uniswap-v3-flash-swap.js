const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("🚀 Deploying Uniswap V3 Flash Swap Contract...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  
  // Check balance
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");
  
  if (balance < ethers.parseEther("0.01")) {
    console.error("❌ Insufficient balance for deployment. Need at least 0.01 ETH");
    console.error("Current balance:", ethers.formatEther(balance), "ETH");
    process.exit(1);
  }

  if (balance < ethers.parseEther("0.05")) {
    console.warn("⚠️  Low balance detected. Deployment may fail if gas costs are high.");
    console.warn("Recommended: At least 0.05 ETH for safe deployment");
  }
  
  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId.toString());
  
  // Only deploy on mainnet
  if (network.chainId !== 1n) {
    console.error("❌ This contract should only be deployed on Ethereum mainnet (Chain ID: 1)");
    console.error("Current network Chain ID:", network.chainId.toString());
    process.exit(1);
  }
  
  try {
    // Deploy the contract
    console.log("\n📦 Deploying UniswapV3FlashSwap contract...");
    
    const UniswapV3FlashSwap = await ethers.getContractFactory("UniswapV3FlashSwap");
    
    // Estimate gas for deployment
    const deploymentData = UniswapV3FlashSwap.getDeployTransaction();
    let gasEstimate;
    try {
      gasEstimate = await deployer.estimateGas(deploymentData);
    } catch (error) {
      console.warn("Gas estimation failed, using default:", error.message);
      gasEstimate = 200000n; // Default gas limit
    }

    const gasPrice = await deployer.provider.getFeeData();

    // Use higher gas limit for safety
    const safeGasLimit = Math.max(Number(gasEstimate) * 6, 350000);
    console.log("Estimated gas:", gasEstimate.toString());
    console.log("Safe gas limit:", safeGasLimit);
    console.log("Gas price:", ethers.formatUnits(gasPrice.gasPrice || 0n, "gwei"), "gwei");

    const estimatedCost = BigInt(safeGasLimit) * (gasPrice.gasPrice || 0n);
    console.log("Estimated deployment cost:", ethers.formatEther(estimatedCost), "ETH");
    
    // Check if cost exceeds $30 (assuming ETH = $2000)
    const costUSD = Number(ethers.formatEther(estimatedCost)) * 2000;
    console.log("Estimated cost in USD:", costUSD.toFixed(2));

    if (costUSD > 30) {
      console.error(`❌ Deployment cost ($${costUSD.toFixed(2)}) exceeds $30 limit`);
      console.error("Cancelling deployment to prevent excessive costs");
      process.exit(1);
    }

    // Check if we have enough balance for deployment
    if (estimatedCost > balance) {
      console.error(`❌ Insufficient balance for deployment`);
      console.error(`Required: ${ethers.formatEther(estimatedCost)} ETH`);
      console.error(`Available: ${ethers.formatEther(balance)} ETH`);
      console.error(`Shortfall: ${ethers.formatEther(estimatedCost - balance)} ETH`);
      process.exit(1);
    }
    
    // Deploy with optimized gas settings
    const contract = await UniswapV3FlashSwap.deploy({
      gasLimit: safeGasLimit,
      gasPrice: gasPrice.gasPrice
    });
    
    console.log("⏳ Waiting for deployment transaction...");
    await contract.waitForDeployment();
    
    const contractAddress = await contract.getAddress();
    console.log("✅ UniswapV3FlashSwap deployed to:", contractAddress);
    
    // Verify deployment
    console.log("\n🔍 Verifying deployment...");
    const code = await deployer.provider.getCode(contractAddress);
    if (code === "0x") {
      console.error("❌ Contract deployment failed - no code at address");
      process.exit(1);
    }
    
    console.log("✅ Contract code verified at address");
    
    // Test basic contract functions
    console.log("\n🧪 Testing contract functions...");
    
    try {
      // Test getting pool address for WETH/USDC 0.3%
      const wethAddress = "******************************************";
      const usdcAddress = "******************************************";
      
      const poolAddress = await contract.getPoolAddress(wethAddress, usdcAddress, 3000);
      console.log("WETH/USDC 0.3% pool address:", poolAddress);
      
      if (poolAddress === ethers.ZeroAddress) {
        console.warn("⚠️  WETH/USDC pool not found - this might be expected");
      } else {
        console.log("✅ Pool address lookup working");
      }
    } catch (error) {
      console.warn("⚠️  Contract function test failed:", error.message);
    }
    
    // Update .env file with contract address
    console.log("\n📝 Updating .env file...");
    
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Add or update the contract address
    const contractAddressLine = `UNISWAP_V3_FLASH_SWAP_CONTRACT=${contractAddress}`;
    
    if (envContent.includes('UNISWAP_V3_FLASH_SWAP_CONTRACT=')) {
      // Replace existing line
      envContent = envContent.replace(
        /UNISWAP_V3_FLASH_SWAP_CONTRACT=.*/,
        contractAddressLine
      );
    } else {
      // Add new line
      envContent += `\n# Uniswap V3 Flash Swap Contract\n${contractAddressLine}\n`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log("✅ .env file updated with contract address");
    
    // Save deployment info
    const deploymentInfo = {
      contractAddress,
      deploymentBlock: await deployer.provider.getBlockNumber(),
      deploymentTime: new Date().toISOString(),
      deployer: deployer.address,
      network: network.name,
      chainId: network.chainId.toString(),
      gasUsed: gasEstimate.toString(),
      deploymentCost: ethers.formatEther(estimatedCost),
      deploymentCostUSD: costUSD.toFixed(2)
    };
    
    const deploymentPath = path.join(__dirname, '..', 'deployments', 'uniswap-v3-flash-swap.json');
    
    // Create deployments directory if it doesn't exist
    const deploymentsDir = path.dirname(deploymentPath);
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
    console.log("✅ Deployment info saved to:", deploymentPath);
    
    // Display summary
    console.log("\n🎉 DEPLOYMENT SUCCESSFUL!");
    console.log("==========================================");
    console.log("Contract Address:", contractAddress);
    console.log("Network:", network.name);
    console.log("Chain ID:", network.chainId.toString());
    console.log("Deployer:", deployer.address);
    console.log("Gas Used:", gasEstimate.toString());
    console.log("Deployment Cost:", ethers.formatEther(estimatedCost), "ETH");
    console.log("Deployment Cost USD:", `$${costUSD.toFixed(2)}`);
    console.log("==========================================");
    
    console.log("\n📋 NEXT STEPS:");
    console.log("1. ✅ Contract address added to .env file");
    console.log("2. 🔄 Restart your MEV bot to use the new contract");
    console.log("3. 🧪 Test with small amounts first");
    console.log("4. 📊 Monitor for flash swap opportunities");
    
    console.log("\n⚠️  IMPORTANT NOTES:");
    console.log("- This contract performs arbitrage between Uniswap V3 fee tiers");
    console.log("- Only works with the specific trading pairs: WETH/USDC, WETH/USDT, WBTC/WETH, DAI/USDC");
    console.log("- Requires sufficient liquidity in both fee tiers");
    console.log("- Monitor gas costs and profitability carefully");
    
  } catch (error) {
    console.error("❌ Deployment failed:", error.message);
    
    if (error.message.includes("insufficient funds")) {
      console.error("💡 Solution: Add more ETH to your deployer account");
    } else if (error.message.includes("gas")) {
      console.error("💡 Solution: Try increasing gas limit or gas price");
    } else if (error.message.includes("nonce")) {
      console.error("💡 Solution: Wait a moment and try again");
    }
    
    process.exit(1);
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment script failed:", error);
    process.exit(1);
  });
