const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("🚀 Deploying Simple Uniswap V3 Flash Swap Contract...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  
  // Check balance
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");
  
  if (balance < ethers.parseEther("0.005")) {
    console.error("❌ Insufficient balance for deployment. Need at least 0.005 ETH");
    console.error("Current balance:", ethers.formatEther(balance), "ETH");
    process.exit(1);
  }
  
  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId.toString());
  
  // Only deploy on mainnet
  if (network.chainId !== 1n) {
    console.error("❌ This contract should only be deployed on Ethereum mainnet (Chain ID: 1)");
    console.error("Current network Chain ID:", network.chainId.toString());
    process.exit(1);
  }
  
  try {
    // Deploy the contract
    console.log("\n📦 Deploying SimpleUniswapV3FlashSwap contract...");
    
    const SimpleUniswapV3FlashSwap = await ethers.getContractFactory("SimpleUniswapV3FlashSwap");
    
    // Get current gas price
    const gasPrice = await deployer.provider.getFeeData();
    console.log("Current gas price:", ethers.formatUnits(gasPrice.gasPrice || 0n, "gwei"), "gwei");
    
    // Deploy with conservative gas settings
    const contract = await SimpleUniswapV3FlashSwap.deploy({
      gasLimit: 500000, // Conservative gas limit
      gasPrice: gasPrice.gasPrice
    });
    
    console.log("⏳ Waiting for deployment transaction...");
    const deploymentReceipt = await contract.waitForDeployment();
    
    const contractAddress = await contract.getAddress();
    console.log("✅ SimpleUniswapV3FlashSwap deployed to:", contractAddress);
    
    // Get deployment transaction details
    const deployTx = await deployer.provider.getTransaction(contract.deploymentTransaction().hash);
    const receipt = await deployTx.wait();
    
    console.log("Gas used:", receipt.gasUsed.toString());
    console.log("Gas price:", ethers.formatUnits(receipt.gasPrice, "gwei"), "gwei");
    console.log("Deployment cost:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "ETH");
    
    // Verify deployment
    console.log("\n🔍 Verifying deployment...");
    const code = await deployer.provider.getCode(contractAddress);
    if (code === "0x") {
      console.error("❌ Contract deployment failed - no code at address");
      process.exit(1);
    }
    
    console.log("✅ Contract code verified at address");
    
    // Test basic contract functions
    console.log("\n🧪 Testing contract functions...");
    
    try {
      // Test getting pool address for WETH/USDC 0.3%
      const wethAddress = "******************************************";
      const usdcAddress = "******************************************";
      
      const poolAddress = await contract.getPoolAddress(wethAddress, usdcAddress, 3000);
      console.log("WETH/USDC 0.3% pool address:", poolAddress);
      
      if (poolAddress === ethers.ZeroAddress) {
        console.warn("⚠️  WETH/USDC pool not found");
      } else {
        console.log("✅ Pool address lookup working");
      }
      
      // Test owner
      const owner = await contract.owner();
      console.log("Contract owner:", owner);
      console.log("Deployer address:", deployer.address);
      
      if (owner.toLowerCase() === deployer.address.toLowerCase()) {
        console.log("✅ Owner correctly set");
      } else {
        console.warn("⚠️  Owner mismatch");
      }
      
    } catch (error) {
      console.warn("⚠️  Contract function test failed:", error.message);
    }
    
    // Update .env file with contract address
    console.log("\n📝 Updating .env file...");
    
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Add or update the contract address
    const contractAddressLine = `UNISWAP_V3_FLASH_SWAP_CONTRACT=${contractAddress}`;
    
    if (envContent.includes('UNISWAP_V3_FLASH_SWAP_CONTRACT=')) {
      // Replace existing line
      envContent = envContent.replace(
        /UNISWAP_V3_FLASH_SWAP_CONTRACT=.*/,
        contractAddressLine
      );
    } else {
      // Add new line
      envContent += `\n# Uniswap V3 Flash Swap Contract\n${contractAddressLine}\n`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log("✅ .env file updated with contract address");
    
    // Save deployment info
    const deploymentInfo = {
      contractAddress,
      deploymentBlock: receipt.blockNumber,
      deploymentTime: new Date().toISOString(),
      deployer: deployer.address,
      network: network.name,
      chainId: network.chainId.toString(),
      gasUsed: receipt.gasUsed.toString(),
      gasPrice: receipt.gasPrice.toString(),
      deploymentCost: ethers.formatEther(receipt.gasUsed * receipt.gasPrice),
      transactionHash: receipt.hash
    };
    
    const deploymentPath = path.join(__dirname, '..', 'deployments', 'simple-uniswap-v3-flash-swap.json');
    
    // Create deployments directory if it doesn't exist
    const deploymentsDir = path.dirname(deploymentPath);
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
    console.log("✅ Deployment info saved to:", deploymentPath);
    
    // Display summary
    console.log("\n🎉 DEPLOYMENT SUCCESSFUL!");
    console.log("==========================================");
    console.log("Contract Address:", contractAddress);
    console.log("Network:", network.name);
    console.log("Chain ID:", network.chainId.toString());
    console.log("Deployer:", deployer.address);
    console.log("Gas Used:", receipt.gasUsed.toString());
    console.log("Gas Price:", ethers.formatUnits(receipt.gasPrice, "gwei"), "gwei");
    console.log("Deployment Cost:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "ETH");
    console.log("Transaction Hash:", receipt.hash);
    console.log("==========================================");
    
    console.log("\n📋 NEXT STEPS:");
    console.log("1. ✅ Contract address added to .env file");
    console.log("2. 🔄 Restart your MEV bot to use the new contract");
    console.log("3. 🧪 Test with small amounts first");
    console.log("4. 📊 Monitor for flash swap opportunities");
    
  } catch (error) {
    console.error("❌ Deployment failed:", error.message);
    
    if (error.message.includes("insufficient funds")) {
      console.error("💡 Solution: Add more ETH to your deployer account");
    } else if (error.message.includes("gas")) {
      console.error("💡 Solution: Try increasing gas limit or gas price");
    } else if (error.message.includes("nonce")) {
      console.error("💡 Solution: Wait a moment and try again");
    }
    
    console.error("\nFull error:", error);
    process.exit(1);
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment script failed:", error);
    process.exit(1);
  });
