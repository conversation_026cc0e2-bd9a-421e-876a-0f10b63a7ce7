#!/bin/bash

# Safe MEV Bot Startup Script
echo "🚀 Starting MEV Bot in Safe Mode..."

# Set safe environment variables
export SPLIT_SCREEN_DASHBOARD=false
export DRY_RUN=true
export SIMULATION_MODE=true
export ENABLE_ETHERS_MEMPOOL=false
export ENABLE_FLASHBOTS_MEMPOOL=false

# Start the bot
echo "📊 Configuration:"
echo "   • Split Screen: Disabled"
echo "   • Dry Run: Enabled"
echo "   • Simulation: Enabled"
echo "   • WebSocket: Disabled"
echo ""

npx ts-node src/index.ts
